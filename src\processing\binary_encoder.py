"""
Binary Encoder - Görüntüleri verimli binary formata kodlama
"""
import asyncio
import time
import base64
import io
from typing import Optional, Dict, Any, Tuple, Union
import numpy as np
import cv2
from PIL import Image
import json
from dataclasses import dataclass, asdict


@dataclass
class EncodingStats:
    """Encoding istatistikleri"""
    frames_encoded: int = 0
    total_input_bytes: int = 0
    total_output_bytes: int = 0
    avg_encoding_time: float = 0.0
    avg_compression_ratio: float = 0.0
    last_encoding_time: float = 0.0


class BinaryEncoder:
    """
    Görüntüleri verimli binary formata kodlayan sınıf
    """
    
    def __init__(self, format: str = "JPEG", quality: int = 85, 
                 optimize: bool = True, progressive: bool = False):
        """
        Args:
            format: Görüntü formatı ("JPEG", "PNG", "WEBP")
            quality: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> (1-100, JPEG için)
            optimize: Optimizasyon etkin mi
            progressive: Progressive encoding (JPEG için)
        """
        self.format = format.upper()
        self.quality = quality
        self.optimize = optimize
        self.progressive = progressive
        self.stats = EncodingStats()
        self._encoding_times = []
        
        # Format-specific parameters
        self._setup_encoding_params()
    
    def _setup_encoding_params(self) -> None:
        """Format-specific encoding parametrelerini ayarla"""
        if self.format == "JPEG":
            self.cv2_params = [
                cv2.IMWRITE_JPEG_QUALITY, self.quality,
                cv2.IMWRITE_JPEG_PROGRESSIVE, int(self.progressive),
                cv2.IMWRITE_JPEG_OPTIMIZE, int(self.optimize)
            ]
            self.pil_params = {
                "format": "JPEG",
                "quality": self.quality,
                "optimize": self.optimize,
                "progressive": self.progressive
            }
        
        elif self.format == "PNG":
            compression = 9 if self.optimize else 6
            self.cv2_params = [
                cv2.IMWRITE_PNG_COMPRESSION, compression
            ]
            self.pil_params = {
                "format": "PNG",
                "optimize": self.optimize
            }
        
        elif self.format == "WEBP":
            self.cv2_params = [
                cv2.IMWRITE_WEBP_QUALITY, self.quality
            ]
            self.pil_params = {
                "format": "WEBP",
                "quality": self.quality,
                "optimize": self.optimize
            }
    
    async def encode_frame(self, frame: np.ndarray, 
                          metadata: Optional[Dict[str, Any]] = None) -> Optional[bytes]:
        """
        Frame'i binary formata kodla
        
        Args:
            frame: NumPy array olarak görüntü
            metadata: Ek metadata bilgileri
            
        Returns:
            bytes: Kodlanmış binary data veya None
        """
        if frame is None:
            return None
        
        start_time = time.time()
        
        try:
            # OpenCV ile encoding (daha hızlı)
            success, encoded_img = cv2.imencode(f'.{self.format.lower()}', 
                                              frame, self.cv2_params)
            
            if not success:
                return None
            
            # Binary data'yı al
            binary_data = encoded_img.tobytes()
            
            # Metadata ekle (opsiyonel)
            if metadata:
                encoded_data = self._add_metadata(binary_data, metadata)
            else:
                encoded_data = binary_data
            
            # İstatistikleri güncelle
            encoding_time = time.time() - start_time
            self._update_stats(frame.nbytes, len(encoded_data), encoding_time)
            
            return encoded_data
            
        except Exception as e:
            print(f"Encoding error: {e}")
            return None
    
    async def encode_frame_base64(self, frame: np.ndarray,
                                 metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        Frame'i base64 string olarak kodla
        
        Args:
            frame: NumPy array olarak görüntü
            metadata: Ek metadata bilgileri
            
        Returns:
            str: Base64 encoded string veya None
        """
        binary_data = await self.encode_frame(frame, metadata)
        if binary_data:
            return base64.b64encode(binary_data).decode('utf-8')
        return None
    
    def _add_metadata(self, binary_data: bytes, metadata: Dict[str, Any]) -> bytes:
        """Binary data'ya metadata ekle"""
        try:
            # Metadata'yı JSON olarak serialize et
            metadata_json = json.dumps(metadata).encode('utf-8')
            metadata_length = len(metadata_json)
            
            # Format: [metadata_length:4bytes][metadata][binary_data]
            result = (metadata_length.to_bytes(4, 'big') + 
                     metadata_json + binary_data)
            
            return result
        except Exception as e:
            print(f"Metadata encoding error: {e}")
            return binary_data
    
    def decode_with_metadata(self, encoded_data: bytes) -> Tuple[bytes, Optional[Dict[str, Any]]]:
        """
        Metadata ile kodlanmış data'yı decode et
        
        Args:
            encoded_data: Kodlanmış binary data
            
        Returns:
            Tuple[bytes, Optional[Dict]]: (binary_data, metadata)
        """
        try:
            if len(encoded_data) < 4:
                return encoded_data, None
            
            # Metadata length'i oku
            metadata_length = int.from_bytes(encoded_data[:4], 'big')
            
            if metadata_length == 0 or len(encoded_data) < 4 + metadata_length:
                return encoded_data, None
            
            # Metadata'yı oku
            metadata_bytes = encoded_data[4:4 + metadata_length]
            metadata = json.loads(metadata_bytes.decode('utf-8'))
            
            # Binary data'yı al
            binary_data = encoded_data[4 + metadata_length:]
            
            return binary_data, metadata
            
        except Exception as e:
            print(f"Metadata decoding error: {e}")
            return encoded_data, None
    
    async def decode_frame(self, encoded_data: bytes) -> Optional[np.ndarray]:
        """
        Binary data'yı frame'e decode et
        
        Args:
            encoded_data: Kodlanmış binary data
            
        Returns:
            np.ndarray: Decode edilmiş frame veya None
        """
        try:
            # Metadata varsa ayır
            binary_data, _ = self.decode_with_metadata(encoded_data)
            
            # NumPy array'e çevir
            nparr = np.frombuffer(binary_data, np.uint8)
            
            # OpenCV ile decode et
            frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            return frame
            
        except Exception as e:
            print(f"Decoding error: {e}")
            return None
    
    def _update_stats(self, input_size: int, output_size: int, encoding_time: float) -> None:
        """İstatistikleri güncelle"""
        self.stats.frames_encoded += 1
        self.stats.total_input_bytes += input_size
        self.stats.total_output_bytes += output_size
        self.stats.last_encoding_time = encoding_time
        
        # Encoding time tracking
        self._encoding_times.append(encoding_time)
        if len(self._encoding_times) > 100:
            self._encoding_times.pop(0)
        
        self.stats.avg_encoding_time = sum(self._encoding_times) / len(self._encoding_times)
        
        # Compression ratio
        if self.stats.total_input_bytes > 0:
            self.stats.avg_compression_ratio = (
                self.stats.total_output_bytes / self.stats.total_input_bytes
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """İstatistikleri döndür"""
        stats_dict = asdict(self.stats)
        stats_dict.update({
            "format": self.format,
            "quality": self.quality,
            "optimize": self.optimize,
            "progressive": self.progressive,
            "avg_encoding_time_ms": round(self.stats.avg_encoding_time * 1000, 2),
            "compression_ratio_percent": round(self.stats.avg_compression_ratio * 100, 2)
        })
        return stats_dict
    
    def reset_stats(self) -> None:
        """İstatistikleri sıfırla"""
        self.stats = EncodingStats()
        self._encoding_times = []
    
    def set_quality(self, quality: int) -> None:
        """Kalite ayarla ve parametreleri güncelle"""
        self.quality = max(1, min(100, quality))
        self._setup_encoding_params()
    
    def set_format(self, format: str) -> None:
        """Format ayarla ve parametreleri güncelle"""
        self.format = format.upper()
        self._setup_encoding_params()
    
    async def benchmark_encoding(self, frame: np.ndarray, iterations: int = 10) -> Dict[str, float]:
        """
        Encoding performansını test et
        
        Args:
            frame: Test frame'i
            iterations: Test sayısı
            
        Returns:
            Dict: Benchmark sonuçları
        """
        times = []
        sizes = []
        
        for _ in range(iterations):
            start_time = time.time()
            encoded = await self.encode_frame(frame)
            end_time = time.time()
            
            if encoded:
                times.append(end_time - start_time)
                sizes.append(len(encoded))
        
        if not times:
            return {"error": "No successful encodings"}
        
        return {
            "avg_time_ms": round(sum(times) / len(times) * 1000, 2),
            "min_time_ms": round(min(times) * 1000, 2),
            "max_time_ms": round(max(times) * 1000, 2),
            "avg_size_bytes": round(sum(sizes) / len(sizes)),
            "compression_ratio": round(sum(sizes) / len(sizes) / frame.nbytes, 3),
            "iterations": len(times)
        }
