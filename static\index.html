<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-RealTimeStream</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-video"></i> AI-RealTimeStream</h1>
                <div class="connection-status">
                    <span id="connectionStatus" class="status-disconnected">
                        <i class="fas fa-circle"></i> Bağlantı Kesildi
                    </span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Control Panel -->
            <div class="control-panel">
                <div class="panel-section">
                    <h3><i class="fas fa-cog"></i> Kontrol Paneli</h3>
                    
                    <!-- Connection Controls -->
                    <div class="control-group">
                        <label>WebSocket Bağlantısı:</label>
                        <div class="button-group">
                            <button id="connectBtn" class="btn btn-primary">
                                <i class="fas fa-plug"></i> Bağlan
                            </button>
                            <button id="disconnectBtn" class="btn btn-secondary" disabled>
                                <i class="fas fa-unlink"></i> Bağlantıyı Kes
                            </button>
                        </div>
                    </div>

                    <!-- Stream Controls -->
                    <div class="control-group">
                        <label>Video Stream:</label>
                        <div class="button-group">
                            <button id="startStreamBtn" class="btn btn-success" disabled>
                                <i class="fas fa-play"></i> Başlat
                            </button>
                            <button id="stopStreamBtn" class="btn btn-danger" disabled>
                                <i class="fas fa-stop"></i> Durdur
                            </button>
                        </div>
                    </div>

                    <!-- AI Controls -->
                    <div class="control-group">
                        <label>AI Analizi:</label>
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="enableAI" checked>
                                <span class="checkmark"></span>
                                AI Analizini Etkinleştir
                            </label>
                        </div>
                        <div class="slider-group">
                            <label>Güven Eşiği: <span id="confidenceValue">0.5</span></label>
                            <input type="range" id="confidenceSlider" min="0.1" max="1.0" step="0.1" value="0.5">
                        </div>
                    </div>

                    <!-- Subscription Controls -->
                    <div class="control-group">
                        <label>Kanallar:</label>
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="subscribeVideo" checked>
                                <span class="checkmark"></span>
                                Video Stream
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="subscribeAI" checked>
                                <span class="checkmark"></span>
                                AI Analizi
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="subscribeStats">
                                <span class="checkmark"></span>
                                Sistem İstatistikleri
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Video Display -->
            <div class="video-container">
                <div class="video-header">
                    <h3><i class="fas fa-desktop"></i> Canlı Görüntü</h3>
                    <div class="video-info">
                        <span id="fpsCounter">FPS: 0</span>
                        <span id="resolutionInfo">Çözünürlük: -</span>
                    </div>
                </div>
                <div class="video-display">
                    <canvas id="videoCanvas" width="1280" height="720"></canvas>
                    <div id="videoOverlay" class="video-overlay">
                        <div class="loading-spinner">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>Video stream bekleniyor...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Analysis Panel -->
            <div class="analysis-panel">
                <div class="panel-header">
                    <h3><i class="fas fa-brain"></i> AI Analizi</h3>
                    <div class="analysis-stats">
                        <span id="detectionCount">Tespit: 0</span>
                        <span id="processingTime">İşleme: 0ms</span>
                    </div>
                </div>
                <div class="analysis-content">
                    <div id="detectionsList" class="detections-list">
                        <p class="no-detections">Henüz tespit edilmiş nesne yok</p>
                    </div>
                </div>
            </div>

            <!-- Statistics Panel -->
            <div class="stats-panel">
                <div class="panel-header">
                    <h3><i class="fas fa-chart-line"></i> İstatistikler</h3>
                    <button id="refreshStatsBtn" class="btn btn-small">
                        <i class="fas fa-refresh"></i> Yenile
                    </button>
                </div>
                <div class="stats-content">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-label">Aktif Bağlantılar</div>
                            <div id="activeConnections" class="stat-value">0</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Toplam Mesaj</div>
                            <div id="totalMessages" class="stat-value">0</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Veri Transferi</div>
                            <div id="dataTransfer" class="stat-value">0 KB</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Sunucu Çalışma Süresi</div>
                            <div id="serverUptime" class="stat-value">0s</div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <p>&copy; 2024 AI-RealTimeStream - Açık Kaynak Gerçek Zamanlı AI Stream Sistemi</p>
                <div class="footer-links">
                    <a href="https://github.com/yourusername/ai-realtime-stream" target="_blank">
                        <i class="fab fa-github"></i> GitHub
                    </a>
                    <a href="#" id="aboutBtn">
                        <i class="fas fa-info-circle"></i> Hakkında
                    </a>
                </div>
            </div>
        </footer>
    </div>

    <!-- Modal -->
    <div id="aboutModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>AI-RealTimeStream Hakkında</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p>AI-RealTimeStream, gerçek zamanlı görüntü yakalama ve AI analizi için geliştirilmiş açık kaynaklı bir sistemdir.</p>
                <h4>Özellikler:</h4>
                <ul>
                    <li>Gerçek zamanlı ekran/webcam yakalama</li>
                    <li>YOLOv8 ile nesne tespiti</li>
                    <li>WebSocket tabanlı düşük gecikme streaming</li>
                    <li>Modüler ve genişletilebilir mimari</li>
                    <li>Web tabanlı görselleştirme arayüzü</li>
                </ul>
                <h4>Teknolojiler:</h4>
                <p>Python, FastAPI, WebSocket, OpenCV, PyTorch, YOLOv8, HTML5 Canvas</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="websocket-client.js"></script>
    <script src="video-renderer.js"></script>
    <script src="ai-visualizer.js"></script>
    <script src="stats-monitor.js"></script>
    <script src="app.js"></script>
</body>
</html>
