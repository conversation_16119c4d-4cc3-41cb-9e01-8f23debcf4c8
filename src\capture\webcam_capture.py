"""
Webcam Capture Module - OpenCV kullanarak webcam yakalama
"""
import asyncio
import time
from typing import Optional, Dict, Any, List
import numpy as np
import cv2

from .base_capture import BaseCapture


class WebcamCapture(BaseCapture):
    """
    OpenCV kullanarak webcam yakalama sınıfı
    """
    
    def __init__(self, width: int = 1280, height: int = 720, fps: int = 30, 
                 camera_index: int = 0, backend: int = cv2.CAP_DSHOW):
        """
        Args:
            width: Hedef genişlik
            height: Hedef yükseklik
            fps: Hedef FPS
            camera_index: Ka<PERSON>a indeksi (0, 1, 2, ...)
            backend: OpenCV backend (Windows için CAP_DSHOW önerilir)
        """
        super().__init__(width, height, fps)
        self.camera_index = camera_index
        self.backend = backend
        self.cap = None
        self.camera_properties = {}
        
    async def initialize(self) -> bool:
        """Webcam'i başlat"""
        try:
            # <PERSON><PERSON><PERSON><PERSON> aç
            self.cap = cv2.VideoCapture(self.camera_index, self.backend)
            
            if not self.cap.isOpened():
                print(f"Failed to open camera {self.camera_index}")
                return False
            
            # Kamera özelliklerini ayarla
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
            self.cap.set(cv2.CAP_PROP_FPS, self.fps)
            
            # Buffer boyutunu küçült (düşük gecikme için)
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            
            # Kamera özelliklerini oku
            self._read_camera_properties()
            
            print(f"Webcam initialized: {self.camera_properties}")
            return True
            
        except Exception as e:
            print(f"Webcam initialization failed: {e}")
            return False
    
    def _read_camera_properties(self) -> None:
        """Kamera özelliklerini oku"""
        if not self.cap:
            return
        
        self.camera_properties = {
            "width": int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            "height": int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            "fps": self.cap.get(cv2.CAP_PROP_FPS),
            "brightness": self.cap.get(cv2.CAP_PROP_BRIGHTNESS),
            "contrast": self.cap.get(cv2.CAP_PROP_CONTRAST),
            "saturation": self.cap.get(cv2.CAP_PROP_SATURATION),
            "hue": self.cap.get(cv2.CAP_PROP_HUE),
            "exposure": self.cap.get(cv2.CAP_PROP_EXPOSURE),
            "auto_exposure": self.cap.get(cv2.CAP_PROP_AUTO_EXPOSURE),
            "backend": self.backend
        }
    
    async def capture_frame(self) -> Optional[np.ndarray]:
        """Webcam'den frame yakala"""
        if not self.cap or not self.cap.isOpened():
            return None
        
        try:
            # Frame oku
            ret, frame = self.cap.read()
            
            if not ret or frame is None:
                return None
            
            # Boyutlandır (gerekirse)
            if frame.shape[:2] != (self.height, self.width):
                frame = cv2.resize(frame, (self.width, self.height))
            
            return frame
            
        except Exception as e:
            print(f"Webcam capture error: {e}")
            return None
    
    async def cleanup(self) -> None:
        """Kaynakları temizle"""
        if self.cap:
            self.cap.release()
            self.cap = None
    
    @staticmethod
    def get_available_cameras() -> List[Dict[str, Any]]:
        """Mevcut kameraları listele"""
        cameras = []
        
        # En fazla 10 kamera indeksini test et
        for i in range(10):
            cap = cv2.VideoCapture(i)
            if cap.isOpened():
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                
                cameras.append({
                    "index": i,
                    "resolution": f"{width}x{height}",
                    "fps": fps,
                    "available": True
                })
                cap.release()
            else:
                cameras.append({
                    "index": i,
                    "available": False
                })
        
        return [cam for cam in cameras if cam["available"]]
    
    def set_camera_property(self, property_id: int, value: float) -> bool:
        """Kamera özelliği ayarla"""
        if not self.cap:
            return False
        
        try:
            result = self.cap.set(property_id, value)
            if result:
                self._read_camera_properties()
            return result
        except Exception as e:
            print(f"Failed to set camera property: {e}")
            return False
    
    def get_camera_property(self, property_id: int) -> Optional[float]:
        """Kamera özelliği oku"""
        if not self.cap:
            return None
        
        try:
            return self.cap.get(property_id)
        except Exception as e:
            print(f"Failed to get camera property: {e}")
            return None
    
    def set_auto_exposure(self, enabled: bool) -> bool:
        """Otomatik pozlama ayarla"""
        value = 0.75 if enabled else 0.25  # OpenCV auto exposure values
        return self.set_camera_property(cv2.CAP_PROP_AUTO_EXPOSURE, value)
    
    def set_brightness(self, brightness: float) -> bool:
        """Parlaklık ayarla (0.0 - 1.0)"""
        return self.set_camera_property(cv2.CAP_PROP_BRIGHTNESS, brightness)
    
    def set_contrast(self, contrast: float) -> bool:
        """Kontrast ayarla (0.0 - 1.0)"""
        return self.set_camera_property(cv2.CAP_PROP_CONTRAST, contrast)
    
    def change_camera(self, camera_index: int) -> bool:
        """Kamera değiştir"""
        if self.is_capturing:
            return False  # Yakalama sırasında değiştirilemez
        
        self.camera_index = camera_index
        return True
    
    async def capture_to_file(self, filename: str) -> bool:
        """Webcam görüntüsünü dosyaya kaydet"""
        frame = await self.capture_frame()
        if frame is not None:
            try:
                cv2.imwrite(filename, frame)
                return True
            except Exception as e:
                print(f"Failed to save webcam image: {e}")
                return False
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Genişletilmiş istatistikler"""
        stats = super().get_stats()
        stats.update({
            "camera_index": self.camera_index,
            "camera_properties": self.camera_properties,
            "capture_type": "webcam"
        })
        return stats
