# AI-RealTimeStream 🚀

AI destekli gerçek zamanlı binary stream sistemi. Ekran/webcam görüntülerini yakalayıp, AI ile analiz edip, gerç<PERSON> zamanlı olarak WebSocket üzerinden dağıtan açık kaynaklı bir sistem.

## 🎯 Özellikler

- **Gerçek Zamanlı Görüntü Yakalama**: Ekran veya webcam görüntülerini yüksek performansla yakalar
- **AI Destekli Analiz**: YOLOv8 ile nesne tespiti ve görüntü analizi
- **WebSocket Streaming**: Düşük gecikme süreli gerçek zamanlı veri aktarımı
- **Binary Optimizasyon**: Verimli binary encoding ve sıkıştırma
- **Modüler Mimari**: Genişletilebilir ve özelleştirilebilir bileşenler
- **Web Arayüzü**: Kullanıcı dostu görselleştirme ve kontrol paneli

## 🏗️ Sistem Mimarisi

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Capture       │───▶│   Processing     │───▶│   WebSocket     │
│   Layer         │    │   Layer          │    │   Layer         │
│                 │    │                  │    │                 │
│ • Screen/Webcam │    │ • Binary Encode  │    │ • Real-time     │
│ • MSS/OpenCV    │    │ • AI Analysis    │    │ • Multi-client  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   AI Layer       │
                       │                  │
                       │ • YOLOv8         │
                       │ • Object Detect  │
                       └──────────────────┘
```

## 📁 Proje Yapısı

```
ai-realtime-stream/
├── src/
│   ├── capture/          # Görüntü yakalama modülleri
│   ├── processing/       # Görüntü işleme ve AI
│   ├── websocket/        # WebSocket sunucusu
│   └── main.py          # Ana uygulama
├── static/              # Frontend dosyaları
├── models/              # AI modelleri
├── config/              # Konfigürasyon dosyaları
├── tests/               # Test dosyaları
├── Dockerfile
├── requirements.txt
└── README.md
```

## 🚀 Hızlı Başlangıç

### Gereksinimler

- Python 3.8+
- OpenCV
- PyTorch
- FastAPI

### Kurulum

1. **Repository'yi klonlayın:**
```bash
git clone https://github.com/yourusername/ai-realtime-stream.git
cd ai-realtime-stream
```

2. **Sanal ortam oluşturun:**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# veya
venv\Scripts\activate     # Windows
```

3. **Bağımlılıkları yükleyin:**
```bash
pip install -r requirements.txt
```

4. **Konfigürasyonu ayarlayın:**
```bash
cp .env.example .env
# .env dosyasını düzenleyin
```

5. **Uygulamayı başlatın:**
```bash
python src/main.py
```

6. **Web arayüzünü açın:**
```
http://localhost:8000
```

## 🔧 Konfigürasyon

Temel ayarlar `.env` dosyasında yapılandırılabilir:

- `CAPTURE_SOURCE`: "screen" veya "webcam"
- `CAPTURE_FPS`: Yakalama hızı (FPS)
- `AI_CONFIDENCE_THRESHOLD`: AI güven eşiği
- `ENABLE_AI_PROCESSING`: AI analizini etkinleştir/devre dışı bırak

## 📊 Performans

- **Gecikme**: < 100ms (yerel ağ)
- **FPS**: 30+ (optimum ayarlarda)
- **CPU Kullanımı**: %20-40 (AI etkin)
- **Bellek**: ~500MB (temel model)

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request açın

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için [LICENSE](LICENSE) dosyasına bakın.

## 🙏 Teşekkürler

- [YOLOv8](https://github.com/ultralytics/ultralytics) - Nesne tespiti
- [FastAPI](https://fastapi.tiangolo.com/) - Web framework
- [OpenCV](https://opencv.org/) - Görüntü işleme

---

**Not**: Bu proje aktif geliştirme aşamasındadır. Öneriler ve katkılar memnuniyetle karşılanır!
