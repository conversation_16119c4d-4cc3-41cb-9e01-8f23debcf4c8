"""
Connection Manager - WebSocket bağlantılarını yönetme
"""
import asyncio
import time
import json
from typing import Dict, List, Set, Optional, Any
from dataclasses import dataclass, asdict
from fastapi import WebSocket, WebSocketDisconnect
import logging

logger = logging.getLogger(__name__)


@dataclass
class ClientInfo:
    """İstemci bilgileri"""
    client_id: str
    websocket: WebSocket
    connected_at: float
    last_ping: float
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    subscriptions: Set[str] = None
    
    def __post_init__(self):
        if self.subscriptions is None:
            self.subscriptions = set()


@dataclass
class ConnectionStats:
    """Bağlantı istatistikleri"""
    total_connections: int = 0
    active_connections: int = 0
    total_messages_sent: int = 0
    total_messages_received: int = 0
    total_bytes_sent: int = 0
    total_bytes_received: int = 0
    avg_message_size: float = 0.0
    uptime: float = 0.0


class ConnectionManager:
    """
    WebSocket bağlantılarını yöneten sınıf
    """
    
    def __init__(self, max_connections: int = 100, ping_interval: int = 30):
        """
        Args:
            max_connections: Maksimum bağlantı sayısı
            ping_interval: Ping gönderme aralığı (saniye)
        """
        self.max_connections = max_connections
        self.ping_interval = ping_interval
        self.clients: Dict[str, ClientInfo] = {}
        self.stats = ConnectionStats()
        self.start_time = time.time()
        self._ping_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None
        
        # Subscription channels
        self.channels: Dict[str, Set[str]] = {
            "video_stream": set(),
            "ai_analysis": set(),
            "system_stats": set(),
            "all": set()
        }
    
    async def connect(self, websocket: WebSocket, client_id: str) -> bool:
        """
        Yeni istemci bağlantısı
        
        Args:
            websocket: WebSocket bağlantısı
            client_id: İstemci ID'si
            
        Returns:
            bool: Bağlantı başarılı ise True
        """
        # Maksimum bağlantı kontrolü
        if len(self.clients) >= self.max_connections:
            logger.warning(f"Max connections reached. Rejecting client: {client_id}")
            return False
        
        # Mevcut bağlantı kontrolü
        if client_id in self.clients:
            logger.warning(f"Client already connected: {client_id}")
            await self.disconnect(client_id)
        
        try:
            await websocket.accept()
            
            # İstemci bilgilerini kaydet
            client_info = ClientInfo(
                client_id=client_id,
                websocket=websocket,
                connected_at=time.time(),
                last_ping=time.time(),
                ip_address=websocket.client.host if websocket.client else None
            )
            
            self.clients[client_id] = client_info
            self.stats.total_connections += 1
            self.stats.active_connections = len(self.clients)
            
            # Varsayılan channel'a ekle
            self.channels["all"].add(client_id)
            
            logger.info(f"Client connected: {client_id} from {client_info.ip_address}")
            
            # Ping task'ı başlat (ilk bağlantıda)
            if len(self.clients) == 1:
                await self._start_background_tasks()
            
            # Hoş geldin mesajı gönder
            await self.send_to_client(client_id, {
                "type": "connection_established",
                "client_id": client_id,
                "server_time": time.time(),
                "available_channels": list(self.channels.keys())
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Connection failed for {client_id}: {e}")
            return False
    
    async def disconnect(self, client_id: str) -> None:
        """
        İstemci bağlantısını kapat
        
        Args:
            client_id: İstemci ID'si
        """
        if client_id not in self.clients:
            return
        
        client_info = self.clients[client_id]
        
        try:
            # WebSocket'i kapat
            await client_info.websocket.close()
        except Exception as e:
            logger.error(f"Error closing websocket for {client_id}: {e}")
        
        # Channel'lardan çıkar
        for channel_clients in self.channels.values():
            channel_clients.discard(client_id)
        
        # İstemciyi kaldır
        del self.clients[client_id]
        self.stats.active_connections = len(self.clients)
        
        logger.info(f"Client disconnected: {client_id}")
        
        # Son istemci ayrıldıysa background task'ları durdur
        if len(self.clients) == 0:
            await self._stop_background_tasks()
    
    async def send_to_client(self, client_id: str, message: Dict[str, Any]) -> bool:
        """
        Belirli bir istemciye mesaj gönder
        
        Args:
            client_id: İstemci ID'si
            message: Gönderilecek mesaj
            
        Returns:
            bool: Gönderim başarılı ise True
        """
        if client_id not in self.clients:
            return False
        
        client_info = self.clients[client_id]
        
        try:
            message_json = json.dumps(message)
            await client_info.websocket.send_text(message_json)
            
            # İstatistikleri güncelle
            self.stats.total_messages_sent += 1
            self.stats.total_bytes_sent += len(message_json.encode())
            self._update_avg_message_size()
            
            return True
            
        except WebSocketDisconnect:
            logger.info(f"Client {client_id} disconnected during send")
            await self.disconnect(client_id)
            return False
        except Exception as e:
            logger.error(f"Error sending message to {client_id}: {e}")
            return False
    
    async def send_binary_to_client(self, client_id: str, data: bytes) -> bool:
        """
        Belirli bir istemciye binary data gönder
        
        Args:
            client_id: İstemci ID'si
            data: Binary data
            
        Returns:
            bool: Gönderim başarılı ise True
        """
        if client_id not in self.clients:
            return False
        
        client_info = self.clients[client_id]
        
        try:
            await client_info.websocket.send_bytes(data)
            
            # İstatistikleri güncelle
            self.stats.total_messages_sent += 1
            self.stats.total_bytes_sent += len(data)
            self._update_avg_message_size()
            
            return True
            
        except WebSocketDisconnect:
            logger.info(f"Client {client_id} disconnected during binary send")
            await self.disconnect(client_id)
            return False
        except Exception as e:
            logger.error(f"Error sending binary data to {client_id}: {e}")
            return False
    
    async def broadcast_to_channel(self, channel: str, message: Dict[str, Any]) -> int:
        """
        Belirli bir channel'a mesaj yayınla
        
        Args:
            channel: Channel adı
            message: Gönderilecek mesaj
            
        Returns:
            int: Mesajın gönderildiği istemci sayısı
        """
        if channel not in self.channels:
            return 0
        
        client_ids = self.channels[channel].copy()
        successful_sends = 0
        
        for client_id in client_ids:
            if await self.send_to_client(client_id, message):
                successful_sends += 1
        
        return successful_sends
    
    async def broadcast_binary_to_channel(self, channel: str, data: bytes) -> int:
        """
        Belirli bir channel'a binary data yayınla
        
        Args:
            channel: Channel adı
            data: Binary data
            
        Returns:
            int: Mesajın gönderildiği istemci sayısı
        """
        if channel not in self.channels:
            return 0
        
        client_ids = self.channels[channel].copy()
        successful_sends = 0
        
        for client_id in client_ids:
            if await self.send_binary_to_client(client_id, data):
                successful_sends += 1

        return successful_sends

    async def subscribe_to_channel(self, client_id: str, channel: str) -> bool:
        """
        İstemciyi bir channel'a abone et

        Args:
            client_id: İstemci ID'si
            channel: Channel adı

        Returns:
            bool: Abonelik başarılı ise True
        """
        if client_id not in self.clients:
            return False

        if channel not in self.channels:
            self.channels[channel] = set()

        self.channels[channel].add(client_id)
        self.clients[client_id].subscriptions.add(channel)

        logger.info(f"Client {client_id} subscribed to channel: {channel}")
        return True

    async def unsubscribe_from_channel(self, client_id: str, channel: str) -> bool:
        """
        İstemciyi bir channel'dan çıkar

        Args:
            client_id: İstemci ID'si
            channel: Channel adı

        Returns:
            bool: Çıkarma başarılı ise True
        """
        if client_id not in self.clients or channel not in self.channels:
            return False

        self.channels[channel].discard(client_id)
        self.clients[client_id].subscriptions.discard(channel)

        logger.info(f"Client {client_id} unsubscribed from channel: {channel}")
        return True

    async def handle_message(self, client_id: str, message: str) -> None:
        """
        İstemciden gelen mesajı işle

        Args:
            client_id: İstemci ID'si
            message: Gelen mesaj
        """
        if client_id not in self.clients:
            return

        try:
            data = json.loads(message)
            message_type = data.get("type", "unknown")

            # İstatistikleri güncelle
            self.stats.total_messages_received += 1
            self.stats.total_bytes_received += len(message.encode())

            # Ping güncelle
            self.clients[client_id].last_ping = time.time()

            # Mesaj türüne göre işle
            if message_type == "ping":
                await self.send_to_client(client_id, {"type": "pong", "timestamp": time.time()})

            elif message_type == "subscribe":
                channel = data.get("channel")
                if channel:
                    success = await self.subscribe_to_channel(client_id, channel)
                    await self.send_to_client(client_id, {
                        "type": "subscription_response",
                        "channel": channel,
                        "success": success
                    })

            elif message_type == "unsubscribe":
                channel = data.get("channel")
                if channel:
                    success = await self.unsubscribe_from_channel(client_id, channel)
                    await self.send_to_client(client_id, {
                        "type": "unsubscription_response",
                        "channel": channel,
                        "success": success
                    })

            elif message_type == "get_stats":
                await self.send_to_client(client_id, {
                    "type": "stats_response",
                    "stats": self.get_stats()
                })

            else:
                logger.warning(f"Unknown message type from {client_id}: {message_type}")

        except json.JSONDecodeError:
            logger.error(f"Invalid JSON from {client_id}: {message}")
        except Exception as e:
            logger.error(f"Error handling message from {client_id}: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """İstatistikleri döndür"""
        self.stats.uptime = time.time() - self.start_time
        stats_dict = asdict(self.stats)

        stats_dict.update({
            "max_connections": self.max_connections,
            "ping_interval": self.ping_interval,
            "channels": {
                channel: len(clients) for channel, clients in self.channels.items()
            },
            "clients": {
                client_id: {
                    "connected_at": info.connected_at,
                    "last_ping": info.last_ping,
                    "ip_address": info.ip_address,
                    "subscriptions": list(info.subscriptions)
                }
                for client_id, info in self.clients.items()
            }
        })

        return stats_dict

    def _update_avg_message_size(self) -> None:
        """Ortalama mesaj boyutunu güncelle"""
        total_messages = self.stats.total_messages_sent + self.stats.total_messages_received
        total_bytes = self.stats.total_bytes_sent + self.stats.total_bytes_received

        if total_messages > 0:
            self.stats.avg_message_size = total_bytes / total_messages

    async def _start_background_tasks(self) -> None:
        """Background task'ları başlat"""
        if self._ping_task is None:
            self._ping_task = asyncio.create_task(self._ping_clients())
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._cleanup_disconnected())

    async def _stop_background_tasks(self) -> None:
        """Background task'ları durdur"""
        if self._ping_task:
            self._ping_task.cancel()
            self._ping_task = None
        if self._cleanup_task:
            self._cleanup_task.cancel()
            self._cleanup_task = None

    async def _ping_clients(self) -> None:
        """İstemcilere ping gönder"""
        while True:
            try:
                await asyncio.sleep(self.ping_interval)

                current_time = time.time()
                ping_message = {"type": "ping", "timestamp": current_time}

                for client_id in list(self.clients.keys()):
                    await self.send_to_client(client_id, ping_message)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in ping task: {e}")

    async def _cleanup_disconnected(self) -> None:
        """Bağlantısı kopmuş istemcileri temizle"""
        while True:
            try:
                await asyncio.sleep(60)  # Her dakika kontrol et

                current_time = time.time()
                timeout = self.ping_interval * 3  # 3x ping interval

                disconnected_clients = []
                for client_id, client_info in self.clients.items():
                    if current_time - client_info.last_ping > timeout:
                        disconnected_clients.append(client_id)

                for client_id in disconnected_clients:
                    logger.info(f"Cleaning up disconnected client: {client_id}")
                    await self.disconnect(client_id)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup task: {e}")

    async def shutdown(self) -> None:
        """Tüm bağlantıları kapat ve temizle"""
        logger.info("Shutting down connection manager...")

        # Background task'ları durdur
        await self._stop_background_tasks()

        # Tüm istemcileri bilgilendir
        shutdown_message = {"type": "server_shutdown", "timestamp": time.time()}
        for client_id in list(self.clients.keys()):
            await self.send_to_client(client_id, shutdown_message)
            await self.disconnect(client_id)

        logger.info("Connection manager shutdown complete")
