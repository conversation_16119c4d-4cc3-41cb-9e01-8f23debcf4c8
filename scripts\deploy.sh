#!/bin/bash

# AI-RealTimeStream Deployment Script
# Usage: ./scripts/deploy.sh [environment] [options]

set -e

# Default values
ENVIRONMENT="production"
BUILD_FRESH=false
PULL_LATEST=false
BACKUP_DATA=true
VERBOSE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
AI-RealTimeStream Deployment Script

Usage: $0 [ENVIRONMENT] [OPTIONS]

ENVIRONMENTS:
    production      Deploy production environment (default)
    development     Deploy development environment
    monitoring      Deploy with monitoring stack

OPTIONS:
    --build-fresh   Build images from scratch
    --pull-latest   Pull latest base images
    --no-backup     Skip data backup
    --verbose       Enable verbose output
    --help          Show this help message

EXAMPLES:
    $0                              # Deploy production
    $0 development                  # Deploy development
    $0 production --build-fresh     # Deploy production with fresh build
    $0 monitoring --verbose         # Deploy with monitoring and verbose output

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        production|development|monitoring)
            ENVIRONMENT="$1"
            shift
            ;;
        --build-fresh)
            BUILD_FRESH=true
            shift
            ;;
        --pull-latest)
            PULL_LATEST=true
            shift
            ;;
        --no-backup)
            BACKUP_DATA=false
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Enable verbose mode if requested
if [ "$VERBOSE" = true ]; then
    set -x
fi

print_status "Starting AI-RealTimeStream deployment..."
print_status "Environment: $ENVIRONMENT"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    print_error "docker-compose is not installed. Please install it and try again."
    exit 1
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p logs models cache config/ssl

# Backup existing data if requested
if [ "$BACKUP_DATA" = true ] && [ -d "models" ] && [ "$(ls -A models)" ]; then
    print_status "Backing up existing data..."
    BACKUP_DIR="backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    if [ -d "models" ]; then
        cp -r models "$BACKUP_DIR/"
        print_success "Models backed up to $BACKUP_DIR/models"
    fi
    
    if [ -d "logs" ]; then
        cp -r logs "$BACKUP_DIR/"
        print_success "Logs backed up to $BACKUP_DIR/logs"
    fi
fi

# Pull latest base images if requested
if [ "$PULL_LATEST" = true ]; then
    print_status "Pulling latest base images..."
    docker-compose pull
fi

# Build options
BUILD_ARGS=""
if [ "$BUILD_FRESH" = true ]; then
    BUILD_ARGS="--no-cache"
    print_status "Building images from scratch..."
else
    print_status "Building images..."
fi

# Set up environment-specific configuration
case $ENVIRONMENT in
    production)
        print_status "Deploying production environment..."
        docker-compose -f docker-compose.yml build $BUILD_ARGS
        docker-compose -f docker-compose.yml up -d
        ;;
    development)
        print_status "Deploying development environment..."
        docker-compose -f docker-compose.yml build $BUILD_ARGS ai-realtime-stream-dev
        docker-compose -f docker-compose.yml --profile development up -d
        ;;
    monitoring)
        print_status "Deploying with monitoring stack..."
        docker-compose -f docker-compose.yml build $BUILD_ARGS
        docker-compose -f docker-compose.yml --profile monitoring --profile production up -d
        ;;
esac

# Wait for services to be healthy
print_status "Waiting for services to be healthy..."
sleep 10

# Check service health
print_status "Checking service health..."
if docker-compose ps | grep -q "Up (healthy)"; then
    print_success "Services are healthy!"
else
    print_warning "Some services may not be fully healthy yet. Check with 'docker-compose ps'"
fi

# Show running services
print_status "Running services:"
docker-compose ps

# Show useful URLs
print_success "Deployment completed successfully!"
echo ""
echo "Useful URLs:"
echo "  Main Application: http://localhost:8000"
echo "  Health Check:     http://localhost:8000/health"
echo "  API Stats:        http://localhost:8000/api/stats"

if [ "$ENVIRONMENT" = "monitoring" ]; then
    echo "  Prometheus:       http://localhost:9090"
    echo "  Grafana:          http://localhost:3000 (admin/admin123)"
fi

if [ "$ENVIRONMENT" = "development" ]; then
    echo "  Jupyter:          http://localhost:8888"
fi

echo ""
echo "Useful commands:"
echo "  View logs:        docker-compose logs -f"
echo "  Stop services:    docker-compose down"
echo "  Restart:          docker-compose restart"
echo "  Update:           ./scripts/deploy.sh $ENVIRONMENT --pull-latest"

print_success "AI-RealTimeStream is now running!"
