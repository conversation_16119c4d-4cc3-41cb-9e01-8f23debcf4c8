"""
Stream Manager - Video stream'i ve AI analizini yönetir
"""
import asyncio
import time
import logging
from typing import Dict, Optional, Any
from dataclasses import dataclass, asdict

from capture.capture_factory import CaptureFactory
from capture.base_capture import BaseCapture
from processing.ai_analyzer import AIAnalyzer
from processing.image_processor import ImageProcessor
from processing.binary_encoder import BinaryEncoder
from websocket.websocket_server import WebSocketServer

logger = logging.getLogger(__name__)


@dataclass
class StreamSession:
    """Stream oturumu bilgileri"""
    client_id: str
    capture: BaseCapture
    is_active: bool = False
    ai_enabled: bool = True
    confidence_threshold: float = 0.5
    start_time: float = 0.0
    frame_count: int = 0
    last_frame_time: float = 0.0


@dataclass
class StreamStats:
    """Stream istatistikleri"""
    active_sessions: int = 0
    total_frames_processed: int = 0
    total_ai_analyses: int = 0
    avg_fps: float = 0.0
    avg_processing_time: float = 0.0
    total_uptime: float = 0.0


class StreamManager:
    """
    Video stream'lerini ve AI analizini yöneten sınıf
    """
    
    def __init__(self, websocket_server: WebSocketServer,
                 ai_analyzer: Optional[AIAnalyzer] = None,
                 image_processor: Optional[ImageProcessor] = None,
                 binary_encoder: Optional[BinaryEncoder] = None):
        """
        Args:
            websocket_server: WebSocket sunucusu
            ai_analyzer: AI analiz modülü
            image_processor: Görüntü işleme modülü
            binary_encoder: Binary kodlama modülü
        """
        self.websocket_server = websocket_server
        self.ai_analyzer = ai_analyzer
        self.image_processor = image_processor
        self.binary_encoder = binary_encoder
        
        # Active sessions
        self.sessions: Dict[str, StreamSession] = {}
        
        # Global stream task
        self.stream_task: Optional[asyncio.Task] = None
        self.is_streaming = False
        
        # Statistics
        self.stats = StreamStats()
        self.start_time = time.time()
        
        # Performance tracking
        self._processing_times = []
        self._fps_buffer = []
        
        # Settings
        self.target_fps = 30
        self.max_sessions = 10
        self.frame_skip_threshold = 100  # ms
    
    async def start_stream(self, client_id: str, capture_source: str = "screen",
                          ai_enabled: bool = True, confidence_threshold: float = 0.5,
                          **capture_kwargs) -> bool:
        """
        İstemci için stream başlat
        
        Args:
            client_id: İstemci ID'si
            capture_source: Yakalama kaynağı ("screen" veya "webcam")
            ai_enabled: AI analizi etkin mi
            confidence_threshold: AI güven eşiği
            **capture_kwargs: Yakalama için ek parametreler
            
        Returns:
            bool: Başlatma başarılı ise True
        """
        try:
            # Maksimum oturum kontrolü
            if len(self.sessions) >= self.max_sessions:
                logger.warning(f"Maximum sessions reached, rejecting {client_id}")
                return False
            
            # Mevcut oturum kontrolü
            if client_id in self.sessions:
                logger.info(f"Stopping existing session for {client_id}")
                await self.stop_stream(client_id)
            
            # Capture oluştur
            capture = CaptureFactory.create_capture(capture_source, **capture_kwargs)
            if not capture:
                logger.error(f"Failed to create capture for {capture_source}")
                return False
            
            # Capture'ı başlat
            if not await capture.start_capture():
                logger.error(f"Failed to start capture for {client_id}")
                return False
            
            # Session oluştur
            session = StreamSession(
                client_id=client_id,
                capture=capture,
                is_active=True,
                ai_enabled=ai_enabled and self.ai_analyzer is not None,
                confidence_threshold=confidence_threshold,
                start_time=time.time()
            )
            
            self.sessions[client_id] = session
            self.stats.active_sessions = len(self.sessions)
            
            # Global stream task başlat (ilk oturum için)
            if not self.is_streaming:
                await self.start_global_stream()
            
            logger.info(f"Stream started for {client_id} with {capture_source}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting stream for {client_id}: {e}")
            return False
    
    async def stop_stream(self, client_id: str) -> bool:
        """
        İstemci stream'ini durdur
        
        Args:
            client_id: İstemci ID'si
            
        Returns:
            bool: Durdurma başarılı ise True
        """
        try:
            if client_id not in self.sessions:
                logger.warning(f"No active session for {client_id}")
                return False
            
            session = self.sessions[client_id]
            session.is_active = False
            
            # Capture'ı durdur
            await session.capture.stop_capture()
            
            # Session'ı kaldır
            del self.sessions[client_id]
            self.stats.active_sessions = len(self.sessions)
            
            # Son oturum kapandıysa global stream'i durdur
            if len(self.sessions) == 0:
                await self.stop_global_stream()
            
            logger.info(f"Stream stopped for {client_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping stream for {client_id}: {e}")
            return False
    
    async def start_global_stream(self):
        """Global stream task'ını başlat"""
        if self.is_streaming:
            return
        
        self.is_streaming = True
        self.stream_task = asyncio.create_task(self.stream_loop())
        logger.info("Global stream started")
    
    async def stop_global_stream(self):
        """Global stream task'ını durdur"""
        if not self.is_streaming:
            return
        
        self.is_streaming = False
        
        if self.stream_task:
            self.stream_task.cancel()
            try:
                await self.stream_task
            except asyncio.CancelledError:
                pass
            self.stream_task = None
        
        logger.info("Global stream stopped")
    
    async def stream_loop(self):
        """Ana stream döngüsü"""
        frame_interval = 1.0 / self.target_fps
        
        while self.is_streaming:
            loop_start = time.time()
            
            try:
                # Aktif oturumları işle
                active_sessions = [s for s in self.sessions.values() if s.is_active]
                
                if not active_sessions:
                    await asyncio.sleep(0.1)
                    continue
                
                # Her oturum için frame işle
                for session in active_sessions:
                    await self.process_session_frame(session)
                
                # FPS kontrolü
                processing_time = time.time() - loop_start
                sleep_time = max(0, frame_interval - processing_time)
                
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                
                # İstatistikleri güncelle
                self.update_performance_stats(processing_time)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in stream loop: {e}")
                await asyncio.sleep(0.1)
    
    async def process_session_frame(self, session: StreamSession):
        """Oturum için frame işle"""
        try:
            # Frame yakala
            frame_result = await session.capture.capture_with_timing()
            if not frame_result:
                return
            
            frame, capture_time = frame_result
            session.frame_count += 1
            session.last_frame_time = time.time()
            
            # Görüntü işleme
            if self.image_processor:
                frame = await self.image_processor.process_frame(frame)
                if frame is None:
                    return
            
            # Binary encoding
            if self.binary_encoder:
                frame_data = await self.binary_encoder.encode_frame_base64(frame)
                if not frame_data:
                    return
            else:
                # Fallback: OpenCV encoding
                import cv2
                import base64
                _, buffer = cv2.imencode('.jpg', frame)
                frame_data = base64.b64encode(buffer).decode('utf-8')
            
            # Video frame gönder
            await self.websocket_server.send_to_client(session.client_id, {
                "type": "video_frame",
                "data": frame_data,
                "timestamp": time.time(),
                "metadata": {
                    "frame_count": session.frame_count,
                    "capture_time": capture_time,
                    "resolution": session.capture.get_resolution()
                }
            })
            
            # AI analizi
            if session.ai_enabled and self.ai_analyzer:
                await self.process_ai_analysis(session, frame)
            
            self.stats.total_frames_processed += 1
            
        except Exception as e:
            logger.error(f"Error processing frame for {session.client_id}: {e}")
    
    async def process_ai_analysis(self, session: StreamSession, frame):
        """AI analizi işle"""
        try:
            analysis_result = await self.ai_analyzer.analyze_frame(frame)
            if not analysis_result:
                return
            
            # Güven eşiğine göre filtrele
            filtered_detections = [
                detection for detection in analysis_result.detections
                if detection.confidence >= session.confidence_threshold
            ]
            
            # AI analiz sonucunu gönder
            await self.websocket_server.send_to_client(session.client_id, {
                "type": "ai_analysis",
                "detections": [
                    {
                        "class_id": d.class_id,
                        "class_name": d.class_name,
                        "confidence": d.confidence,
                        "bbox": d.bbox,
                        "center": d.center
                    }
                    for d in filtered_detections
                ],
                "processing_time": analysis_result.processing_time,
                "timestamp": time.time(),
                "metadata": {
                    "total_detections": len(analysis_result.detections),
                    "filtered_detections": len(filtered_detections),
                    "confidence_threshold": session.confidence_threshold
                }
            })
            
            self.stats.total_ai_analyses += 1
            
        except Exception as e:
            logger.error(f"Error in AI analysis for {session.client_id}: {e}")
    
    def update_performance_stats(self, processing_time: float):
        """Performans istatistiklerini güncelle"""
        # Processing time tracking
        self._processing_times.append(processing_time)
        if len(self._processing_times) > 100:
            self._processing_times.pop(0)
        
        self.stats.avg_processing_time = sum(self._processing_times) / len(self._processing_times)
        
        # FPS calculation
        current_time = time.time()
        self._fps_buffer.append(current_time)
        
        # Keep last 30 frames for FPS calculation
        if len(self._fps_buffer) > 30:
            self._fps_buffer.pop(0)
        
        if len(self._fps_buffer) > 1:
            time_span = self._fps_buffer[-1] - self._fps_buffer[0]
            if time_span > 0:
                self.stats.avg_fps = (len(self._fps_buffer) - 1) / time_span
        
        # Total uptime
        self.stats.total_uptime = current_time - self.start_time
    
    def get_stats(self) -> Dict[str, Any]:
        """İstatistikleri döndür"""
        stats_dict = asdict(self.stats)
        
        # Session details
        session_details = {}
        for client_id, session in self.sessions.items():
            session_details[client_id] = {
                "is_active": session.is_active,
                "ai_enabled": session.ai_enabled,
                "confidence_threshold": session.confidence_threshold,
                "frame_count": session.frame_count,
                "uptime": time.time() - session.start_time,
                "capture_stats": session.capture.get_stats()
            }
        
        stats_dict.update({
            "sessions": session_details,
            "is_streaming": self.is_streaming,
            "target_fps": self.target_fps,
            "components": {
                "ai_analyzer": self.ai_analyzer is not None,
                "image_processor": self.image_processor is not None,
                "binary_encoder": self.binary_encoder is not None
            }
        })
        
        return stats_dict
    
    async def shutdown(self):
        """Stream manager'ı kapat"""
        logger.info("Shutting down stream manager...")
        
        # Tüm oturumları durdur
        for client_id in list(self.sessions.keys()):
            await self.stop_stream(client_id)
        
        # Global stream'i durdur
        await self.stop_global_stream()
        
        logger.info("Stream manager shutdown complete")
    
    def get_session_info(self, client_id: str) -> Optional[Dict[str, Any]]:
        """Oturum bilgilerini döndür"""
        if client_id not in self.sessions:
            return None
        
        session = self.sessions[client_id]
        return {
            "client_id": client_id,
            "is_active": session.is_active,
            "ai_enabled": session.ai_enabled,
            "confidence_threshold": session.confidence_threshold,
            "frame_count": session.frame_count,
            "uptime": time.time() - session.start_time,
            "capture_type": type(session.capture).__name__,
            "capture_stats": session.capture.get_stats()
        }
    
    async def update_session_settings(self, client_id: str, **settings) -> bool:
        """Oturum ayarlarını güncelle"""
        if client_id not in self.sessions:
            return False
        
        session = self.sessions[client_id]
        
        if "ai_enabled" in settings:
            session.ai_enabled = settings["ai_enabled"] and self.ai_analyzer is not None
        
        if "confidence_threshold" in settings:
            session.confidence_threshold = max(0.0, min(1.0, settings["confidence_threshold"]))
        
        logger.info(f"Updated settings for {client_id}: {settings}")
        return True
