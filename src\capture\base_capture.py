"""
Base Capture Class - Tüm yakalama sınıfları için temel interface
"""
import asyncio
import time
from abc import ABC, abstractmethod
from typing import Optional, Tuple, Any, Dict
import numpy as np
from dataclasses import dataclass


@dataclass
class CaptureStats:
    """Yakalama istatistikleri"""
    frames_captured: int = 0
    frames_dropped: int = 0
    avg_fps: float = 0.0
    avg_capture_time: float = 0.0
    last_capture_time: float = 0.0
    total_bytes: int = 0


class BaseCapture(ABC):
    """
    Tüm yakalama sınıfları için temel abstract class
    """
    
    def __init__(self, width: int = 1280, height: int = 720, fps: int = 30):
        self.width = width
        self.height = height
        self.fps = fps
        self.is_capturing = False
        self.stats = CaptureStats()
        self._capture_times = []
        self._last_frame_time = 0
        
    @abstractmethod
    async def initialize(self) -> bool:
        """
        Yakalama cihazını başlat
        Returns:
            bool: Başlatma başarılı ise True
        """
        pass
    
    @abstractmethod
    async def capture_frame(self) -> Optional[np.ndarray]:
        """
        Tek bir frame yakala
        Returns:
            Optional[np.ndarray]: Yakalanan frame veya None
        """
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """
        Kaynakları temizle
        """
        pass
    
    async def start_capture(self) -> None:
        """Yakalamayı başlat"""
        if not await self.initialize():
            raise RuntimeError("Capture initialization failed")
        
        self.is_capturing = True
        self.stats = CaptureStats()
        
    async def stop_capture(self) -> None:
        """Yakalamayı durdur"""
        self.is_capturing = False
        await self.cleanup()
    
    def get_target_frame_time(self) -> float:
        """Hedef frame süresi (saniye)"""
        return 1.0 / self.fps
    
    def update_stats(self, capture_time: float, frame_size: int) -> None:
        """İstatistikleri güncelle"""
        current_time = time.time()
        
        self.stats.frames_captured += 1
        self.stats.total_bytes += frame_size
        self.stats.last_capture_time = capture_time
        
        # Capture time tracking
        self._capture_times.append(capture_time)
        if len(self._capture_times) > 100:  # Son 100 frame'i tut
            self._capture_times.pop(0)
        
        self.stats.avg_capture_time = sum(self._capture_times) / len(self._capture_times)
        
        # FPS calculation
        if self._last_frame_time > 0:
            frame_interval = current_time - self._last_frame_time
            if frame_interval > 0:
                current_fps = 1.0 / frame_interval
                # Exponential moving average
                alpha = 0.1
                self.stats.avg_fps = (alpha * current_fps + 
                                    (1 - alpha) * self.stats.avg_fps)
        
        self._last_frame_time = current_time
    
    def get_stats(self) -> Dict[str, Any]:
        """İstatistikleri dict olarak döndür"""
        return {
            "frames_captured": self.stats.frames_captured,
            "frames_dropped": self.stats.frames_dropped,
            "avg_fps": round(self.stats.avg_fps, 2),
            "avg_capture_time": round(self.stats.avg_capture_time * 1000, 2),  # ms
            "total_bytes": self.stats.total_bytes,
            "is_capturing": self.is_capturing,
            "resolution": f"{self.width}x{self.height}",
            "target_fps": self.fps
        }
    
    def get_resolution(self) -> Tuple[int, int]:
        """Çözünürlük bilgisini döndür"""
        return (self.width, self.height)
    
    def set_resolution(self, width: int, height: int) -> None:
        """Çözünürlük ayarla"""
        self.width = width
        self.height = height
    
    def set_fps(self, fps: int) -> None:
        """FPS ayarla"""
        self.fps = fps
    
    async def capture_with_timing(self) -> Optional[Tuple[np.ndarray, float]]:
        """
        Frame yakalama ve timing bilgisi
        Returns:
            Optional[Tuple[np.ndarray, float]]: (frame, capture_time) veya None
        """
        if not self.is_capturing:
            return None
        
        start_time = time.time()
        frame = await self.capture_frame()
        capture_time = time.time() - start_time
        
        if frame is not None:
            frame_size = frame.nbytes if hasattr(frame, 'nbytes') else 0
            self.update_stats(capture_time, frame_size)
            return frame, capture_time
        else:
            self.stats.frames_dropped += 1
            return None
