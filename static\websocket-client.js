/**
 * WebSocket Client - WebSocket bağlantısını yönetir
 */

class WebSocketClient {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.clientId = this.generateClientId();
        
        // Event handlers
        this.onConnect = null;
        this.onDisconnect = null;
        this.onMessage = null;
        this.onError = null;
        
        // Message handlers
        this.messageHandlers = new Map();
        
        // Statistics
        this.stats = {
            messagesReceived: 0,
            messagesSent: 0,
            bytesReceived: 0,
            bytesSent: 0,
            connectionTime: null,
            lastPing: null
        };
        
        this.setupDefaultHandlers();
    }
    
    generateClientId() {
        return 'client_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }
    
    setupDefaultHandlers() {
        // Ping/Pong handler
        this.registerHandler('ping', (data) => {
            this.send({
                type: 'pong',
                timestamp: Date.now()
            });
        });
        
        this.registerHandler('pong', (data) => {
            this.stats.lastPing = Date.now() - data.timestamp;
        });
        
        // Connection established handler
        this.registerHandler('connection_established', (data) => {
            console.log('Connection established:', data);
            this.clientId = data.client_id;
        });
        
        // Error handler
        this.registerHandler('error', (data) => {
            console.error('Server error:', data);
        });
        
        // Server shutdown handler
        this.registerHandler('server_shutdown', (data) => {
            console.warn('Server is shutting down');
            this.disconnect();
        });
    }
    
    connect(url = null) {
        if (this.isConnected || this.isConnecting) {
            console.warn('Already connected or connecting');
            return Promise.resolve();
        }
        
        return new Promise((resolve, reject) => {
            try {
                this.isConnecting = true;
                this.updateConnectionStatus('connecting');
                
                // WebSocket URL oluştur
                const wsUrl = url || this.getWebSocketUrl();
                
                console.log('Connecting to:', wsUrl);
                this.ws = new WebSocket(wsUrl);
                
                // Connection timeout
                const connectionTimeout = setTimeout(() => {
                    if (!this.isConnected) {
                        this.ws.close();
                        reject(new Error('Connection timeout'));
                    }
                }, 10000);
                
                this.ws.onopen = (event) => {
                    clearTimeout(connectionTimeout);
                    this.isConnected = true;
                    this.isConnecting = false;
                    this.reconnectAttempts = 0;
                    this.stats.connectionTime = Date.now();
                    
                    console.log('WebSocket connected');
                    this.updateConnectionStatus('connected');
                    
                    if (this.onConnect) {
                        this.onConnect(event);
                    }
                    
                    resolve();
                };
                
                this.ws.onmessage = (event) => {
                    this.handleMessage(event);
                };
                
                this.ws.onclose = (event) => {
                    clearTimeout(connectionTimeout);
                    this.isConnected = false;
                    this.isConnecting = false;
                    
                    console.log('WebSocket disconnected:', event.code, event.reason);
                    this.updateConnectionStatus('disconnected');
                    
                    if (this.onDisconnect) {
                        this.onDisconnect(event);
                    }
                    
                    // Auto-reconnect
                    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
                        this.scheduleReconnect();
                    }
                    
                    if (this.isConnecting) {
                        reject(new Error(`Connection failed: ${event.code} ${event.reason}`));
                    }
                };
                
                this.ws.onerror = (event) => {
                    console.error('WebSocket error:', event);
                    
                    if (this.onError) {
                        this.onError(event);
                    }
                    
                    if (this.isConnecting) {
                        reject(new Error('WebSocket error'));
                    }
                };
                
            } catch (error) {
                this.isConnecting = false;
                reject(error);
            }
        });
    }
    
    disconnect() {
        if (this.ws) {
            this.ws.close(1000, 'Client disconnect');
            this.ws = null;
        }
        this.isConnected = false;
        this.isConnecting = false;
        this.reconnectAttempts = this.maxReconnectAttempts; // Prevent auto-reconnect
    }
    
    send(message) {
        if (!this.isConnected || !this.ws) {
            console.warn('Cannot send message: not connected');
            return false;
        }
        
        try {
            const messageStr = JSON.stringify(message);
            this.ws.send(messageStr);
            
            // Update statistics
            this.stats.messagesSent++;
            this.stats.bytesSent += messageStr.length;
            
            return true;
        } catch (error) {
            console.error('Error sending message:', error);
            return false;
        }
    }
    
    sendBinary(data) {
        if (!this.isConnected || !this.ws) {
            console.warn('Cannot send binary data: not connected');
            return false;
        }
        
        try {
            this.ws.send(data);
            
            // Update statistics
            this.stats.messagesSent++;
            this.stats.bytesSent += data.byteLength || data.length;
            
            return true;
        } catch (error) {
            console.error('Error sending binary data:', error);
            return false;
        }
    }
    
    handleMessage(event) {
        try {
            // Update statistics
            this.stats.messagesReceived++;
            this.stats.bytesReceived += event.data.length;
            
            // Parse JSON message
            const message = JSON.parse(event.data);
            const messageType = message.type;
            
            // Call specific handler
            if (this.messageHandlers.has(messageType)) {
                this.messageHandlers.get(messageType)(message);
            }
            
            // Call general message handler
            if (this.onMessage) {
                this.onMessage(message);
            }
            
        } catch (error) {
            console.error('Error handling message:', error);
        }
    }
    
    registerHandler(messageType, handler) {
        this.messageHandlers.set(messageType, handler);
    }
    
    unregisterHandler(messageType) {
        this.messageHandlers.delete(messageType);
    }
    
    subscribe(channel) {
        return this.send({
            type: 'subscribe',
            channel: channel
        });
    }
    
    unsubscribe(channel) {
        return this.send({
            type: 'unsubscribe',
            channel: channel
        });
    }
    
    ping() {
        return this.send({
            type: 'ping',
            timestamp: Date.now()
        });
    }
    
    getStats() {
        return this.send({
            type: 'get_stats'
        });
    }
    
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            if (!this.isConnected) {
                this.connect().catch(error => {
                    console.error('Reconnection failed:', error);
                });
            }
        }, delay);
    }
    
    getWebSocketUrl() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host;
        return `${protocol}//${host}/ws/${this.clientId}`;
    }
    
    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connectionStatus');
        if (statusElement) {
            statusElement.className = `status-${status}`;
            
            const statusText = {
                'connected': '🟢 Bağlandı',
                'connecting': '🟡 Bağlanıyor...',
                'disconnected': '🔴 Bağlantı Kesildi'
            };
            
            statusElement.innerHTML = `<i class="fas fa-circle"></i> ${statusText[status] || status}`;
        }
    }
    
    getConnectionStats() {
        return {
            ...this.stats,
            isConnected: this.isConnected,
            clientId: this.clientId,
            reconnectAttempts: this.reconnectAttempts,
            uptime: this.stats.connectionTime ? Date.now() - this.stats.connectionTime : 0
        };
    }
}

// Global WebSocket client instance
window.wsClient = new WebSocketClient();
