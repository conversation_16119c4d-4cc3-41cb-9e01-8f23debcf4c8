# AI-RealTimeStream Environment Configuration
# Copy this file to .env and modify values as needed

# Server Configuration
HOST=localhost
PORT=8000
DEBUG=true

# Capture Configuration
CAPTURE_FPS=30
CAPTURE_WIDTH=1280
CAPTURE_HEIGHT=720
CAPTURE_SOURCE=screen
WEBCAM_INDEX=0

# AI Configuration
AI_MODEL_PATH=models/yolov8n.pt
AI_CONFIDENCE_THRESHOLD=0.5
ENABLE_AI_PROCESSING=true

# Performance Configuration
PROCESSING_THREADS=4
MAX_QUEUE_SIZE=100
TARGET_LATENCY_MS=100

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
ENABLE_PERFORMANCE_LOGGING=true
