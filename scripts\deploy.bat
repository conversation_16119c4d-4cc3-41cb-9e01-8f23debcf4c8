@echo off
REM AI-RealTimeStream Windows Deployment Script
REM Usage: deploy.bat [environment] [options]

setlocal enabledelayedexpansion

REM Default values
set ENVIRONMENT=production
set BUILD_FRESH=false
set PULL_LATEST=false
set BACKUP_DATA=true
set VERBOSE=false

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :start_deployment
if "%~1"=="production" (
    set ENVIRONMENT=production
    shift
    goto :parse_args
)
if "%~1"=="development" (
    set ENVIRONMENT=development
    shift
    goto :parse_args
)
if "%~1"=="monitoring" (
    set ENVIRONMENT=monitoring
    shift
    goto :parse_args
)
if "%~1"=="--build-fresh" (
    set BUILD_FRESH=true
    shift
    goto :parse_args
)
if "%~1"=="--pull-latest" (
    set PULL_LATEST=true
    shift
    goto :parse_args
)
if "%~1"=="--no-backup" (
    set BACKUP_DATA=false
    shift
    goto :parse_args
)
if "%~1"=="--verbose" (
    set VERBOSE=true
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    goto :show_usage
)
echo [ERROR] Unknown option: %~1
goto :show_usage

:show_usage
echo AI-RealTimeStream Windows Deployment Script
echo.
echo Usage: %~nx0 [ENVIRONMENT] [OPTIONS]
echo.
echo ENVIRONMENTS:
echo     production      Deploy production environment (default)
echo     development     Deploy development environment
echo     monitoring      Deploy with monitoring stack
echo.
echo OPTIONS:
echo     --build-fresh   Build images from scratch
echo     --pull-latest   Pull latest base images
echo     --no-backup     Skip data backup
echo     --verbose       Enable verbose output
echo     --help          Show this help message
echo.
echo EXAMPLES:
echo     %~nx0                              # Deploy production
echo     %~nx0 development                  # Deploy development
echo     %~nx0 production --build-fresh     # Deploy production with fresh build
echo     %~nx0 monitoring --verbose         # Deploy with monitoring and verbose output
echo.
goto :eof

:start_deployment
echo [INFO] Starting AI-RealTimeStream deployment...
echo [INFO] Environment: %ENVIRONMENT%

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker Desktop and try again.
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] docker-compose is not installed. Please install it and try again.
    exit /b 1
)

REM Create necessary directories
echo [INFO] Creating necessary directories...
if not exist "logs" mkdir logs
if not exist "models" mkdir models
if not exist "cache" mkdir cache
if not exist "config" mkdir config
if not exist "config\ssl" mkdir config\ssl

REM Backup existing data if requested
if "%BACKUP_DATA%"=="true" (
    if exist "models" (
        dir /b models | findstr /r ".*" >nul
        if not errorlevel 1 (
            echo [INFO] Backing up existing data...
            set BACKUP_DIR=backup\%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
            set BACKUP_DIR=!BACKUP_DIR: =0!
            mkdir "!BACKUP_DIR!"
            
            if exist "models" (
                xcopy /E /I models "!BACKUP_DIR!\models" >nul
                echo [SUCCESS] Models backed up to !BACKUP_DIR!\models
            )
            
            if exist "logs" (
                xcopy /E /I logs "!BACKUP_DIR!\logs" >nul
                echo [SUCCESS] Logs backed up to !BACKUP_DIR!\logs
            )
        )
    )
)

REM Pull latest base images if requested
if "%PULL_LATEST%"=="true" (
    echo [INFO] Pulling latest base images...
    docker-compose pull
)

REM Build options
set BUILD_ARGS=
if "%BUILD_FRESH%"=="true" (
    set BUILD_ARGS=--no-cache
    echo [INFO] Building images from scratch...
) else (
    echo [INFO] Building images...
)

REM Set up environment-specific configuration
if "%ENVIRONMENT%"=="production" (
    echo [INFO] Deploying production environment...
    docker-compose -f docker-compose.yml build %BUILD_ARGS%
    docker-compose -f docker-compose.yml up -d
) else if "%ENVIRONMENT%"=="development" (
    echo [INFO] Deploying development environment...
    docker-compose -f docker-compose.yml build %BUILD_ARGS% ai-realtime-stream-dev
    docker-compose -f docker-compose.yml --profile development up -d
) else if "%ENVIRONMENT%"=="monitoring" (
    echo [INFO] Deploying with monitoring stack...
    docker-compose -f docker-compose.yml build %BUILD_ARGS%
    docker-compose -f docker-compose.yml --profile monitoring --profile production up -d
)

REM Wait for services to be healthy
echo [INFO] Waiting for services to be healthy...
timeout /t 10 /nobreak >nul

REM Check service health
echo [INFO] Checking service health...
docker-compose ps | findstr "Up (healthy)" >nul
if not errorlevel 1 (
    echo [SUCCESS] Services are healthy!
) else (
    echo [WARNING] Some services may not be fully healthy yet. Check with 'docker-compose ps'
)

REM Show running services
echo [INFO] Running services:
docker-compose ps

REM Show useful URLs
echo.
echo [SUCCESS] Deployment completed successfully!
echo.
echo Useful URLs:
echo   Main Application: http://localhost:8000
echo   Health Check:     http://localhost:8000/health
echo   API Stats:        http://localhost:8000/api/stats

if "%ENVIRONMENT%"=="monitoring" (
    echo   Prometheus:       http://localhost:9090
    echo   Grafana:          http://localhost:3000 (admin/admin123)
)

if "%ENVIRONMENT%"=="development" (
    echo   Jupyter:          http://localhost:8888
)

echo.
echo Useful commands:
echo   View logs:        docker-compose logs -f
echo   Stop services:    docker-compose down
echo   Restart:          docker-compose restart
echo   Update:           scripts\deploy.bat %ENVIRONMENT% --pull-latest

echo.
echo [SUCCESS] AI-RealTimeStream is now running!

endlocal
