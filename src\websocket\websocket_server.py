"""
WebSocket Server - FastAPI WebSocket sunucusu
"""
import asyncio
import uuid
import time
from typing import Dict, Any, Optional
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import logging

from .connection_manager import ConnectionManager
from .message_handler import MessageHandler

logger = logging.getLogger(__name__)


class WebSocketServer:
    """
    FastAPI tabanlı WebSocket sunucusu
    """
    
    def __init__(self, app: FastAPI, cors_origins: list = None):
        """
        Args:
            app: FastAPI uygulaması
            cors_origins: CORS için izin verilen origin'ler
        """
        self.app = app
        self.connection_manager = ConnectionManager()
        self.message_handler = MessageHandler()
        self.is_running = False
        self.start_time = time.time()
        
        # CORS middleware ekle
        if cors_origins:
            app.add_middleware(
                CORSMiddleware,
                allow_origins=cors_origins,
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
        
        # WebSocket endpoint'lerini kaydet
        self._register_endpoints()
        
        # Message handler'ları kaydet
        self._register_message_handlers()
    
    def _register_endpoints(self) -> None:
        """WebSocket endpoint'lerini kaydet"""
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            await self.handle_websocket_connection(websocket)
        
        @self.app.websocket("/ws/{client_id}")
        async def websocket_endpoint_with_id(websocket: WebSocket, client_id: str):
            await self.handle_websocket_connection(websocket, client_id)
        
        @self.app.get("/api/stats")
        async def get_server_stats():
            return self.get_server_stats()
        
        @self.app.get("/api/health")
        async def health_check():
            return {
                "status": "healthy",
                "uptime": time.time() - self.start_time,
                "active_connections": len(self.connection_manager.clients),
                "timestamp": time.time()
            }
    
    def _register_message_handlers(self) -> None:
        """Message handler'ları kaydet"""
        
        async def handle_ping(client_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
            return {"type": "pong", "timestamp": time.time()}
        
        async def handle_subscribe(client_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
            channel = data.get("channel")
            success = await self.connection_manager.subscribe_to_channel(client_id, channel)
            return {
                "type": "subscription_response",
                "channel": channel,
                "success": success
            }
        
        async def handle_unsubscribe(client_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
            channel = data.get("channel")
            success = await self.connection_manager.unsubscribe_from_channel(client_id, channel)
            return {
                "type": "unsubscription_response",
                "channel": channel,
                "success": success
            }
        
        async def handle_get_stats(client_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
            return {
                "type": "stats_response",
                "stats": self.get_server_stats()
            }
        
        # Handler'ları kaydet
        self.message_handler.register_handler("ping", handle_ping)
        self.message_handler.register_handler("subscribe", handle_subscribe)
        self.message_handler.register_handler("unsubscribe", handle_unsubscribe)
        self.message_handler.register_handler("get_stats", handle_get_stats)
    
    async def handle_websocket_connection(self, websocket: WebSocket, 
                                        client_id: Optional[str] = None) -> None:
        """
        WebSocket bağlantısını işle
        
        Args:
            websocket: WebSocket bağlantısı
            client_id: İstemci ID'si (opsiyonel)
        """
        # Client ID oluştur
        if not client_id:
            client_id = str(uuid.uuid4())
        
        # Bağlantıyı kabul et
        connection_success = await self.connection_manager.connect(websocket, client_id)
        if not connection_success:
            await websocket.close(code=1008, reason="Connection rejected")
            return
        
        try:
            while True:
                # Mesaj bekle
                message = await websocket.receive_text()
                
                # Mesajı işle
                response = await self.message_handler.process_message(client_id, message)
                
                # Yanıt gönder (varsa)
                if response:
                    await self.connection_manager.send_to_client(client_id, response)
                
        except WebSocketDisconnect:
            logger.info(f"Client {client_id} disconnected")
        except Exception as e:
            logger.error(f"Error handling websocket for {client_id}: {e}")
        finally:
            await self.connection_manager.disconnect(client_id)
    
    async def broadcast_video_frame(self, frame_data: str, 
                                  metadata: Optional[Dict[str, Any]] = None) -> int:
        """
        Video frame'i tüm video_stream abonelerine yayınla
        
        Args:
            frame_data: Base64 encoded frame data
            metadata: Ek metadata
            
        Returns:
            int: Mesajın gönderildiği istemci sayısı
        """
        message = self.message_handler.create_video_frame_message(frame_data, metadata=metadata)
        return await self.connection_manager.broadcast_to_channel("video_stream", message)
    
    async def broadcast_ai_analysis(self, detections: list, processing_time: float) -> int:
        """
        AI analiz sonucunu tüm ai_analysis abonelerine yayınla
        
        Args:
            detections: Tespit edilen nesneler
            processing_time: İşleme süresi
            
        Returns:
            int: Mesajın gönderildiği istemci sayısı
        """
        message = self.message_handler.create_ai_analysis_message(detections, processing_time)
        return await self.connection_manager.broadcast_to_channel("ai_analysis", message)
    
    async def broadcast_system_stats(self, stats: Dict[str, Any]) -> int:
        """
        Sistem istatistiklerini tüm system_stats abonelerine yayınla
        
        Args:
            stats: Sistem istatistikleri
            
        Returns:
            int: Mesajın gönderildiği istemci sayısı
        """
        message = self.message_handler.create_system_stats_message(stats)
        return await self.connection_manager.broadcast_to_channel("system_stats", message)
    
    async def send_to_client(self, client_id: str, message: Dict[str, Any]) -> bool:
        """
        Belirli bir istemciye mesaj gönder
        
        Args:
            client_id: İstemci ID'si
            message: Gönderilecek mesaj
            
        Returns:
            bool: Gönderim başarılı ise True
        """
        return await self.connection_manager.send_to_client(client_id, message)
    
    async def send_binary_to_client(self, client_id: str, data: bytes) -> bool:
        """
        Belirli bir istemciye binary data gönder
        
        Args:
            client_id: İstemci ID'si
            data: Binary data
            
        Returns:
            bool: Gönderim başarılı ise True
        """
        return await self.connection_manager.send_binary_to_client(client_id, data)
    
    def get_server_stats(self) -> Dict[str, Any]:
        """Sunucu istatistiklerini döndür"""
        return {
            "server": {
                "uptime": time.time() - self.start_time,
                "is_running": self.is_running,
                "start_time": self.start_time
            },
            "connections": self.connection_manager.get_stats(),
            "message_handler": self.message_handler.get_stats()
        }
    
    def get_active_clients(self) -> list:
        """Aktif istemcileri döndür"""
        return list(self.connection_manager.clients.keys())
    
    def get_channel_subscribers(self, channel: str) -> list:
        """Belirli bir channel'ın abonelerini döndür"""
        if channel in self.connection_manager.channels:
            return list(self.connection_manager.channels[channel])
        return []
    
    async def start(self) -> None:
        """Sunucuyu başlat"""
        self.is_running = True
        self.start_time = time.time()
        logger.info("WebSocket server started")
    
    async def stop(self) -> None:
        """Sunucuyu durdur"""
        self.is_running = False
        await self.connection_manager.shutdown()
        logger.info("WebSocket server stopped")
    
    def register_custom_handler(self, message_type: str, handler) -> None:
        """
        Özel mesaj handler'ı kaydet
        
        Args:
            message_type: Mesaj türü
            handler: Handler fonksiyonu
        """
        self.message_handler.register_handler(message_type, handler)
