"""
AI-RealTimeStream Main Application
Tüm bileşenleri birleştiren ana uygulama
"""
import asyncio
import logging
import signal
import sys
import os
from pathlib import Path
from typing import Optional

# Add src to path
sys.path.append(str(Path(__file__).parent))

from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn

# Import our modules
from config.settings import settings
from websocket.websocket_server import WebSocketServer
from processing.model_manager import ModelManager
from processing.ai_analyzer import AIAnalyzer
from processing.image_processor import ImageProcessor
from processing.binary_encoder import BinaryEncoder
from capture.capture_factory import CaptureFactory
from stream_manager import StreamManager

# Setup logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(settings.LOG_FILE) if settings.LOG_FILE else logging.NullHandler()
    ]
)

logger = logging.getLogger(__name__)


class AIRealTimeStreamApp:
    """
    Ana uygulama sınıfı - tüm bileşenleri koordine eder
    """
    
    def __init__(self):
        self.app = FastAPI(
            title="AI-RealTimeStream",
            description="AI destekli gerçek zamanlı binary stream sistemi",
            version="1.0.0"
        )
        
        # Components
        self.websocket_server: Optional[WebSocketServer] = None
        self.model_manager: Optional[ModelManager] = None
        self.ai_analyzer: Optional[AIAnalyzer] = None
        self.image_processor: Optional[ImageProcessor] = None
        self.binary_encoder: Optional[BinaryEncoder] = None
        self.stream_manager: Optional[StreamManager] = None
        
        # State
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        # Setup
        self.setup_app()
        self.setup_signal_handlers()
    
    def setup_app(self):
        """FastAPI uygulamasını yapılandır"""
        
        # Static files
        static_path = Path(__file__).parent.parent / "static"
        if static_path.exists():
            self.app.mount("/static", StaticFiles(directory=str(static_path)), name="static")
        
        # Routes
        @self.app.get("/", response_class=HTMLResponse)
        async def index():
            """Ana sayfa"""
            index_path = static_path / "index.html"
            if index_path.exists():
                return index_path.read_text(encoding='utf-8')
            return "<h1>AI-RealTimeStream</h1><p>Static files not found</p>"
        
        @self.app.get("/health")
        async def health_check():
            """Sağlık kontrolü"""
            return {
                "status": "healthy",
                "version": "1.0.0",
                "components": {
                    "websocket_server": self.websocket_server is not None,
                    "model_manager": self.model_manager is not None,
                    "ai_analyzer": self.ai_analyzer is not None,
                    "stream_manager": self.stream_manager is not None
                }
            }
        
        @self.app.get("/api/config")
        async def get_config():
            """Konfigürasyon bilgilerini döndür"""
            return {
                "capture": {
                    "fps": settings.CAPTURE_FPS,
                    "width": settings.CAPTURE_WIDTH,
                    "height": settings.CAPTURE_HEIGHT,
                    "source": settings.CAPTURE_SOURCE
                },
                "ai": {
                    "enabled": settings.ENABLE_AI_PROCESSING,
                    "model_path": settings.AI_MODEL_PATH,
                    "confidence_threshold": settings.AI_CONFIDENCE_THRESHOLD
                },
                "websocket": {
                    "max_connections": settings.WS_MAX_CONNECTIONS,
                    "ping_interval": settings.WS_PING_INTERVAL
                }
            }
        
        # Startup and shutdown events
        @self.app.on_event("startup")
        async def startup():
            await self.startup()
        
        @self.app.on_event("shutdown")
        async def shutdown():
            await self.shutdown()
    
    def setup_signal_handlers(self):
        """Sinyal işleyicilerini ayarla"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            self.shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def startup(self):
        """Uygulamayı başlat"""
        try:
            logger.info("Starting AI-RealTimeStream application...")
            
            # Initialize components
            await self.initialize_components()
            
            # Setup WebSocket server
            self.websocket_server = WebSocketServer(
                self.app, 
                cors_origins=settings.CORS_ORIGINS
            )
            
            # Setup stream manager
            self.stream_manager = StreamManager(
                websocket_server=self.websocket_server,
                ai_analyzer=self.ai_analyzer,
                image_processor=self.image_processor,
                binary_encoder=self.binary_encoder
            )
            
            # Register custom WebSocket handlers
            self.setup_websocket_handlers()
            
            # Start WebSocket server
            await self.websocket_server.start()
            
            self.is_running = True
            logger.info("Application started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start application: {e}")
            raise
    
    async def initialize_components(self):
        """Bileşenleri başlat"""
        
        # Model Manager
        self.model_manager = ModelManager(
            models_dir=Path(settings.AI_MODEL_PATH).parent,
            device="auto"
        )
        
        # AI Analyzer
        if settings.ENABLE_AI_PROCESSING:
            self.ai_analyzer = AIAnalyzer(
                model_path=settings.AI_MODEL_PATH,
                confidence_threshold=settings.AI_CONFIDENCE_THRESHOLD,
                iou_threshold=settings.AI_IOU_THRESHOLD
            )
            
            # Initialize AI model
            if not await self.ai_analyzer.initialize():
                logger.warning("AI Analyzer initialization failed, continuing without AI")
                self.ai_analyzer = None
        
        # Image Processor
        self.image_processor = ImageProcessor(
            enable_preprocessing=True,
            enable_postprocessing=True
        )
        
        # Binary Encoder
        self.binary_encoder = BinaryEncoder(
            format=settings.COMPRESSION_FORMAT,
            quality=settings.IMAGE_QUALITY,
            optimize=True
        )
        
        logger.info("Components initialized successfully")
    
    def setup_websocket_handlers(self):
        """WebSocket mesaj işleyicilerini ayarla"""
        
        async def handle_start_stream(client_id: str, data: dict):
            """Stream başlatma komutu"""
            try:
                capture_source = data.get('capture_source', settings.CAPTURE_SOURCE)
                ai_enabled = data.get('ai_enabled', settings.ENABLE_AI_PROCESSING)
                confidence_threshold = data.get('confidence_threshold', settings.AI_CONFIDENCE_THRESHOLD)
                
                success = await self.stream_manager.start_stream(
                    client_id=client_id,
                    capture_source=capture_source,
                    ai_enabled=ai_enabled and self.ai_analyzer is not None,
                    confidence_threshold=confidence_threshold
                )
                
                return {
                    "type": "stream_response",
                    "action": "start",
                    "success": success,
                    "message": "Stream started" if success else "Failed to start stream"
                }
                
            except Exception as e:
                logger.error(f"Error starting stream for {client_id}: {e}")
                return {
                    "type": "stream_response",
                    "action": "start",
                    "success": False,
                    "message": f"Error: {str(e)}"
                }
        
        async def handle_stop_stream(client_id: str, data: dict):
            """Stream durdurma komutu"""
            try:
                success = await self.stream_manager.stop_stream(client_id)
                
                return {
                    "type": "stream_response",
                    "action": "stop",
                    "success": success,
                    "message": "Stream stopped" if success else "Failed to stop stream"
                }
                
            except Exception as e:
                logger.error(f"Error stopping stream for {client_id}: {e}")
                return {
                    "type": "stream_response",
                    "action": "stop",
                    "success": False,
                    "message": f"Error: {str(e)}"
                }
        
        async def handle_get_system_info(client_id: str, data: dict):
            """Sistem bilgilerini döndür"""
            try:
                system_info = {
                    "capture_sources": CaptureFactory.get_available_capture_types(),
                    "ai_available": self.ai_analyzer is not None,
                    "models": self.model_manager.get_stats() if self.model_manager else {},
                    "settings": {
                        "capture_fps": settings.CAPTURE_FPS,
                        "capture_resolution": f"{settings.CAPTURE_WIDTH}x{settings.CAPTURE_HEIGHT}",
                        "ai_enabled": settings.ENABLE_AI_PROCESSING,
                        "confidence_threshold": settings.AI_CONFIDENCE_THRESHOLD
                    }
                }
                
                return {
                    "type": "system_info_response",
                    "info": system_info
                }
                
            except Exception as e:
                logger.error(f"Error getting system info: {e}")
                return {
                    "type": "error",
                    "message": f"Failed to get system info: {str(e)}"
                }
        
        # Register handlers
        self.websocket_server.register_custom_handler("start_stream", handle_start_stream)
        self.websocket_server.register_custom_handler("stop_stream", handle_stop_stream)
        self.websocket_server.register_custom_handler("get_system_info", handle_get_system_info)
    
    async def shutdown(self):
        """Uygulamayı kapat"""
        if not self.is_running:
            return
        
        logger.info("Shutting down application...")
        self.is_running = False
        
        try:
            # Stop stream manager
            if self.stream_manager:
                await self.stream_manager.shutdown()
            
            # Stop WebSocket server
            if self.websocket_server:
                await self.websocket_server.stop()
            
            # Cleanup AI components
            if self.ai_analyzer:
                await self.ai_analyzer.cleanup()
            
            # Cleanup model manager
            if self.model_manager:
                await self.model_manager.cleanup()
            
            logger.info("Application shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    async def run(self):
        """Uygulamayı çalıştır"""
        try:
            # Start the server
            config = uvicorn.Config(
                app=self.app,
                host=settings.HOST,
                port=settings.PORT,
                log_level=settings.LOG_LEVEL.lower(),
                access_log=settings.DEBUG
            )
            
            server = uvicorn.Server(config)
            
            # Run server in background
            server_task = asyncio.create_task(server.serve())
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
            # Graceful shutdown
            server.should_exit = True
            await server_task
            
        except Exception as e:
            logger.error(f"Error running application: {e}")
            raise


async def main():
    """Ana fonksiyon"""
    app = AIRealTimeStreamApp()
    await app.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application failed: {e}")
        sys.exit(1)
