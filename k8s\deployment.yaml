# AI-RealTimeStream Kubernetes Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-realtime-stream
  namespace: ai-stream
  labels:
    app: ai-realtime-stream
    version: v1.0.0
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ai-realtime-stream
  template:
    metadata:
      labels:
        app: ai-realtime-stream
        version: v1.0.0
    spec:
      containers:
      - name: ai-realtime-stream
        image: ai-realtime-stream:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        env:
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8000"
        - name: DEBUG
          value: "false"
        - name: LOG_LEVEL
          value: "INFO"
        - name: ENABLE_AI_PROCESSING
          value: "true"
        - name: CAPTURE_SOURCE
          value: "screen"
        - name: CAPTURE_FPS
          value: "30"
        - name: CAPTURE_WIDTH
          value: "1280"
        - name: CAPTURE_HEIGHT
          value: "720"
        - name: REDIS_URL
          value: "redis://redis-service:6379"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        volumeMounts:
        - name: models-storage
          mountPath: /app/models
        - name: logs-storage
          mountPath: /app/logs
        - name: cache-storage
          mountPath: /app/cache
        - name: config-volume
          mountPath: /app/.env
          subPath: .env
          readOnly: true
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      volumes:
      - name: models-storage
        persistentVolumeClaim:
          claimName: models-pvc
      - name: logs-storage
        persistentVolumeClaim:
          claimName: logs-pvc
      - name: cache-storage
        emptyDir: {}
      - name: config-volume
        configMap:
          name: ai-stream-config
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
      - key: "ai-workload"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - ai-realtime-stream
              topologyKey: kubernetes.io/hostname

---
# Redis Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: ai-stream
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /usr/local/etc/redis/redis.conf
          subPath: redis.conf
          readOnly: true
        command:
        - redis-server
        - /usr/local/etc/redis/redis.conf
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-pvc
      - name: redis-config
        configMap:
          name: redis-config

---
# Services
apiVersion: v1
kind: Service
metadata:
  name: ai-realtime-stream-service
  namespace: ai-stream
  labels:
    app: ai-realtime-stream
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: ai-realtime-stream

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: ai-stream
  labels:
    app: redis
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: redis

---
# Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-realtime-stream-ingress
  namespace: ai-stream
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/websocket-services: "ai-realtime-stream-service"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - ai-stream.yourdomain.com
    secretName: ai-stream-tls
  rules:
  - host: ai-stream.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-realtime-stream-service
            port:
              number: 8000
