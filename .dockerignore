# AI-RealTimeStream Docker Ignore File

# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
docs/
*.md

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary folders
tmp/
temp/
cache/

# AI Models (large files)
models/*.pt
models/*.onnx
models/*.trt
models/*.bin

# Screenshots and recordings
screenshots/
recordings/

# Test files
tests/
test/
*_test.py
test_*.py

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Docker
Dockerfile*
docker-compose*
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Deployment
deploy/
deployment/
k8s/
kubernetes/

# Backup files
*.bak
*.backup
*.old

# Large data files
*.csv
*.json
*.xml
*.sql

# Media files
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
*.mkv
*.m4v
*.3gp
*.3g2
*.rm
*.swf
*.vob

# Audio files
*.mp3
*.wav
*.flac
*.aac
*.ogg
*.wma

# Image files (except small ones)
*.raw
*.tiff
*.tif
*.bmp
*.psd
*.ai
*.eps

# Archive files
*.zip
*.tar
*.tar.gz
*.tar.bz2
*.tar.xz
*.rar
*.7z
*.gz
*.bz2
*.xz

# Configuration files that shouldn't be in container
config/local.py
config/development.py
config/production.py
local_settings.py
