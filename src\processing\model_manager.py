"""
Model Manager - AI modellerini yönetme ve optimizasyon
"""
import os
import time
import asyncio
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, asdict
import logging

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class ModelInfo:
    """Model bilgileri"""
    name: str
    path: str
    type: str  # "yolo", "custom", etc.
    device: str
    loaded: bool = False
    load_time: float = 0.0
    memory_usage: int = 0  # bytes
    inference_count: int = 0
    avg_inference_time: float = 0.0


@dataclass
class ModelStats:
    """Model istatistikleri"""
    total_models: int = 0
    loaded_models: int = 0
    total_memory_usage: int = 0
    total_inferences: int = 0
    avg_load_time: float = 0.0


class ModelManager:
    """
    AI modellerini yöneten sınıf
    """
    
    def __init__(self, models_dir: str = "models", device: str = "auto"):
        """
        Args:
            models_dir: Model dosyalarının bulunduğu dizin
            device: Varsayılan cihaz ("cpu", "cuda", "auto")
        """
        self.models_dir = models_dir
        self.device = device if device != "auto" else self._get_best_device()
        self.models: Dict[str, ModelInfo] = {}
        self.loaded_models: Dict[str, Any] = {}
        self.stats = ModelStats()
        
        # Model dizinini oluştur
        os.makedirs(models_dir, exist_ok=True)
        
        # Mevcut modelleri tara
        self._scan_models()
    
    def _get_best_device(self) -> str:
        """En iyi cihazı belirle"""
        if TORCH_AVAILABLE and torch.cuda.is_available():
            return "cuda"
        return "cpu"
    
    def _scan_models(self) -> None:
        """Model dizinindeki modelleri tara"""
        if not os.path.exists(self.models_dir):
            return
        
        for filename in os.listdir(self.models_dir):
            if filename.endswith(('.pt', '.onnx', '.trt')):
                model_path = os.path.join(self.models_dir, filename)
                model_name = os.path.splitext(filename)[0]
                model_type = self._detect_model_type(filename)
                
                model_info = ModelInfo(
                    name=model_name,
                    path=model_path,
                    type=model_type,
                    device=self.device
                )
                
                self.models[model_name] = model_info
                self.stats.total_models += 1
        
        logger.info(f"Found {self.stats.total_models} models in {self.models_dir}")
    
    def _detect_model_type(self, filename: str) -> str:
        """Dosya adından model türünü belirle"""
        filename_lower = filename.lower()
        
        if "yolo" in filename_lower:
            return "yolo"
        elif filename_lower.endswith('.onnx'):
            return "onnx"
        elif filename_lower.endswith('.trt'):
            return "tensorrt"
        else:
            return "pytorch"
    
    async def load_model(self, model_name: str, device: Optional[str] = None) -> bool:
        """
        Model yükle
        
        Args:
            model_name: Model adı
            device: Hedef cihaz (opsiyonel)
            
        Returns:
            bool: Yükleme başarılı ise True
        """
        if model_name not in self.models:
            logger.error(f"Model not found: {model_name}")
            return False
        
        if model_name in self.loaded_models:
            logger.info(f"Model already loaded: {model_name}")
            return True
        
        model_info = self.models[model_name]
        target_device = device or model_info.device
        
        start_time = time.time()
        
        try:
            # Model türüne göre yükle
            if model_info.type == "yolo":
                if not YOLO_AVAILABLE:
                    logger.error("YOLO not available")
                    return False
                
                model = YOLO(model_info.path)
                model.to(target_device)
                
            else:
                logger.error(f"Unsupported model type: {model_info.type}")
                return False
            
            load_time = time.time() - start_time
            
            # Model bilgilerini güncelle
            model_info.loaded = True
            model_info.load_time = load_time
            model_info.device = target_device
            
            # Memory usage hesapla (yaklaşık)
            if TORCH_AVAILABLE and target_device.startswith("cuda"):
                model_info.memory_usage = self._estimate_model_memory(model)
            
            # Model'i kaydet
            self.loaded_models[model_name] = model
            
            # İstatistikleri güncelle
            self.stats.loaded_models += 1
            self.stats.total_memory_usage += model_info.memory_usage
            self._update_avg_load_time()
            
            logger.info(f"Model loaded: {model_name} on {target_device} in {load_time:.2f}s")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load model {model_name}: {e}")
            return False
    
    async def unload_model(self, model_name: str) -> bool:
        """
        Model'i bellekten kaldır
        
        Args:
            model_name: Model adı
            
        Returns:
            bool: Kaldırma başarılı ise True
        """
        if model_name not in self.loaded_models:
            logger.warning(f"Model not loaded: {model_name}")
            return False
        
        try:
            # Model'i sil
            del self.loaded_models[model_name]
            
            # Model bilgilerini güncelle
            if model_name in self.models:
                model_info = self.models[model_name]
                model_info.loaded = False
                self.stats.total_memory_usage -= model_info.memory_usage
                model_info.memory_usage = 0
            
            self.stats.loaded_models -= 1
            
            # GPU memory temizle
            if TORCH_AVAILABLE and torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            logger.info(f"Model unloaded: {model_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unload model {model_name}: {e}")
            return False
    
    def get_model(self, model_name: str) -> Optional[Any]:
        """
        Yüklenmiş model'i al
        
        Args:
            model_name: Model adı
            
        Returns:
            Model instance veya None
        """
        return self.loaded_models.get(model_name)
    
    def is_model_loaded(self, model_name: str) -> bool:
        """Model yüklenmiş mi kontrol et"""
        return model_name in self.loaded_models
    
    def get_available_models(self) -> List[str]:
        """Mevcut modelleri listele"""
        return list(self.models.keys())
    
    def get_loaded_models(self) -> List[str]:
        """Yüklenmiş modelleri listele"""
        return list(self.loaded_models.keys())
    
    async def download_model(self, model_name: str, url: str) -> bool:
        """
        Model indir
        
        Args:
            model_name: Model adı
            url: İndirme URL'i
            
        Returns:
            bool: İndirme başarılı ise True
        """
        try:
            import urllib.request
            
            model_path = os.path.join(self.models_dir, f"{model_name}.pt")
            
            logger.info(f"Downloading model: {model_name} from {url}")
            urllib.request.urlretrieve(url, model_path)
            
            # Model bilgisini ekle
            model_info = ModelInfo(
                name=model_name,
                path=model_path,
                type=self._detect_model_type(f"{model_name}.pt"),
                device=self.device
            )
            
            self.models[model_name] = model_info
            self.stats.total_models += 1
            
            logger.info(f"Model downloaded: {model_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to download model {model_name}: {e}")
            return False
    
    def _estimate_model_memory(self, model) -> int:
        """Model memory kullanımını tahmin et"""
        try:
            if hasattr(model, 'model') and hasattr(model.model, 'parameters'):
                total_params = sum(p.numel() for p in model.model.parameters())
                # Her parametre için 4 byte (float32) varsay
                return total_params * 4
            return 0
        except:
            return 0
    
    def _update_avg_load_time(self) -> None:
        """Ortalama yükleme süresini güncelle"""
        if self.stats.loaded_models > 0:
            total_load_time = sum(
                info.load_time for info in self.models.values() if info.loaded
            )
            self.stats.avg_load_time = total_load_time / self.stats.loaded_models
    
    def update_inference_stats(self, model_name: str, inference_time: float) -> None:
        """
        Inference istatistiklerini güncelle
        
        Args:
            model_name: Model adı
            inference_time: Inference süresi
        """
        if model_name in self.models:
            model_info = self.models[model_name]
            model_info.inference_count += 1
            
            # Exponential moving average
            alpha = 0.1
            if model_info.avg_inference_time == 0:
                model_info.avg_inference_time = inference_time
            else:
                model_info.avg_inference_time = (
                    alpha * inference_time + 
                    (1 - alpha) * model_info.avg_inference_time
                )
            
            self.stats.total_inferences += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """İstatistikleri döndür"""
        stats_dict = asdict(self.stats)
        
        stats_dict.update({
            "device": self.device,
            "models_dir": self.models_dir,
            "torch_available": TORCH_AVAILABLE,
            "yolo_available": YOLO_AVAILABLE,
            "cuda_available": TORCH_AVAILABLE and torch.cuda.is_available(),
            "models": {
                name: {
                    "type": info.type,
                    "device": info.device,
                    "loaded": info.loaded,
                    "load_time": info.load_time,
                    "memory_usage_mb": info.memory_usage / (1024 * 1024),
                    "inference_count": info.inference_count,
                    "avg_inference_time_ms": info.avg_inference_time * 1000
                }
                for name, info in self.models.items()
            }
        })
        
        return stats_dict
    
    async def cleanup(self) -> None:
        """Tüm modelleri temizle"""
        logger.info("Cleaning up models...")
        
        for model_name in list(self.loaded_models.keys()):
            await self.unload_model(model_name)
        
        if TORCH_AVAILABLE and torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info("Model cleanup complete")
