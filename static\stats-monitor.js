/**
 * Stats Monitor - Sistem istatistiklerini izler ve <PERSON><PERSON><PERSON><PERSON><PERSON>
 */

class StatsMonitor {
    constructor() {
        this.stats = {
            server: {},
            connections: {},
            message_handler: {},
            video: {},
            ai: {}
        };
        
        this.updateInterval = 5000; // 5 saniye
        this.intervalId = null;
        this.isMonitoring = false;
        
        // Chart data for trends
        this.chartData = {
            fps: [],
            detections: [],
            connections: [],
            messages: [],
            timestamps: []
        };
        this.maxDataPoints = 50;
        
        // Event handlers
        this.onStatsUpdate = null;
        this.onError = null;
        
        this.setupEventHandlers();
    }
    
    setupEventHandlers() {
        // Refresh button
        const refreshBtn = document.getElementById('refreshStatsBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.requestStats();
            });
        }
    }
    
    startMonitoring() {
        if (this.isMonitoring) {
            return;
        }
        
        this.isMonitoring = true;
        this.requestStats(); // Initial request
        
        this.intervalId = setInterval(() => {
            this.requestStats();
        }, this.updateInterval);
        
        console.log('Stats monitoring started');
    }
    
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }
        
        this.isMonitoring = false;
        
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        
        console.log('Stats monitoring stopped');
    }
    
    requestStats() {
        if (window.wsClient && window.wsClient.isConnected) {
            window.wsClient.getStats();
        } else {
            // Fallback to HTTP API
            this.fetchStatsFromAPI();
        }
    }
    
    async fetchStatsFromAPI() {
        try {
            const response = await fetch('/api/stats');
            if (response.ok) {
                const stats = await response.json();
                this.updateStats(stats);
            } else {
                console.error('Failed to fetch stats from API');
            }
        } catch (error) {
            console.error('Error fetching stats:', error);
            if (this.onError) {
                this.onError(error);
            }
        }
    }
    
    updateStats(newStats) {
        this.stats = { ...this.stats, ...newStats };
        
        // Update chart data
        this.updateChartData();
        
        // Update UI elements
        this.updateStatsDisplay();
        
        // Call callback
        if (this.onStatsUpdate) {
            this.onStatsUpdate(this.stats);
        }
    }
    
    updateChartData() {
        const timestamp = Date.now();
        
        // Add new data points
        this.chartData.timestamps.push(timestamp);
        this.chartData.fps.push(window.videoRenderer ? window.videoRenderer.fps : 0);
        this.chartData.detections.push(window.aiVisualizer ? window.aiVisualizer.currentDetections.length : 0);
        this.chartData.connections.push(this.stats.connections?.active_connections || 0);
        this.chartData.messages.push(this.stats.connections?.total_messages_sent || 0);
        
        // Keep only recent data points
        Object.keys(this.chartData).forEach(key => {
            if (this.chartData[key].length > this.maxDataPoints) {
                this.chartData[key].shift();
            }
        });
    }
    
    updateStatsDisplay() {
        // Active connections
        const activeConnectionsElement = document.getElementById('activeConnections');
        if (activeConnectionsElement) {
            activeConnectionsElement.textContent = this.stats.connections?.active_connections || 0;
        }
        
        // Total messages
        const totalMessagesElement = document.getElementById('totalMessages');
        if (totalMessagesElement) {
            const sent = this.stats.connections?.total_messages_sent || 0;
            const received = this.stats.connections?.total_messages_received || 0;
            totalMessagesElement.textContent = sent + received;
        }
        
        // Data transfer
        const dataTransferElement = document.getElementById('dataTransfer');
        if (dataTransferElement) {
            const bytesSent = this.stats.connections?.total_bytes_sent || 0;
            const bytesReceived = this.stats.connections?.total_bytes_received || 0;
            const totalBytes = bytesSent + bytesReceived;
            dataTransferElement.textContent = this.formatBytes(totalBytes);
        }
        
        // Server uptime
        const serverUptimeElement = document.getElementById('serverUptime');
        if (serverUptimeElement) {
            const uptime = this.stats.server?.uptime || 0;
            serverUptimeElement.textContent = this.formatDuration(uptime);
        }
    }
    
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    formatDuration(seconds) {
        if (seconds < 60) {
            return `${Math.round(seconds)}s`;
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.round(seconds % 60);
            return `${minutes}m ${remainingSeconds}s`;
        } else {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}h ${minutes}m`;
        }
    }
    
    getPerformanceMetrics() {
        return {
            fps: this.getAverageFromArray(this.chartData.fps),
            avgDetections: this.getAverageFromArray(this.chartData.detections),
            avgConnections: this.getAverageFromArray(this.chartData.connections),
            messageRate: this.getMessageRate(),
            dataRate: this.getDataRate()
        };
    }
    
    getAverageFromArray(arr) {
        if (arr.length === 0) return 0;
        return arr.reduce((sum, val) => sum + val, 0) / arr.length;
    }
    
    getMessageRate() {
        if (this.chartData.messages.length < 2) return 0;
        
        const recent = this.chartData.messages.slice(-10);
        const timeSpan = (this.chartData.timestamps.slice(-1)[0] - this.chartData.timestamps.slice(-10)[0]) / 1000;
        const messageIncrease = recent[recent.length - 1] - recent[0];
        
        return timeSpan > 0 ? messageIncrease / timeSpan : 0;
    }
    
    getDataRate() {
        const bytesSent = this.stats.connections?.total_bytes_sent || 0;
        const bytesReceived = this.stats.connections?.total_bytes_received || 0;
        const totalBytes = bytesSent + bytesReceived;
        const uptime = this.stats.server?.uptime || 1;
        
        return totalBytes / uptime; // bytes per second
    }
    
    exportStats() {
        return {
            timestamp: Date.now(),
            stats: this.stats,
            chartData: this.chartData,
            performance: this.getPerformanceMetrics()
        };
    }
    
    generateReport() {
        const metrics = this.getPerformanceMetrics();
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                avgFPS: Math.round(metrics.fps * 100) / 100,
                avgDetections: Math.round(metrics.avgDetections * 100) / 100,
                avgConnections: Math.round(metrics.avgConnections * 100) / 100,
                messageRate: Math.round(metrics.messageRate * 100) / 100,
                dataRate: this.formatBytes(metrics.dataRate) + '/s'
            },
            current: {
                activeConnections: this.stats.connections?.active_connections || 0,
                totalMessages: (this.stats.connections?.total_messages_sent || 0) + 
                              (this.stats.connections?.total_messages_received || 0),
                totalDataTransfer: this.formatBytes(
                    (this.stats.connections?.total_bytes_sent || 0) + 
                    (this.stats.connections?.total_bytes_received || 0)
                ),
                serverUptime: this.formatDuration(this.stats.server?.uptime || 0)
            },
            trends: {
                fpsHistory: this.chartData.fps.slice(-20),
                detectionHistory: this.chartData.detections.slice(-20),
                connectionHistory: this.chartData.connections.slice(-20)
            }
        };
        
        return report;
    }
    
    downloadReport() {
        const report = this.generateReport();
        const blob = new Blob([JSON.stringify(report, null, 2)], { 
            type: 'application/json' 
        });
        
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `ai-stream-report-${Date.now()}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }
    
    resetStats() {
        this.chartData = {
            fps: [],
            detections: [],
            connections: [],
            messages: [],
            timestamps: []
        };
        
        console.log('Stats data reset');
    }
    
    setUpdateInterval(interval) {
        this.updateInterval = interval;
        
        if (this.isMonitoring) {
            this.stopMonitoring();
            this.startMonitoring();
        }
    }
    
    getStats() {
        return {
            current: this.stats,
            performance: this.getPerformanceMetrics(),
            isMonitoring: this.isMonitoring,
            updateInterval: this.updateInterval,
            dataPoints: this.chartData.timestamps.length
        };
    }
}

// Global stats monitor instance
window.statsMonitor = new StatsMonitor();
