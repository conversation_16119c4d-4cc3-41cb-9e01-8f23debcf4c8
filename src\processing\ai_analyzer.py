"""
AI Analyzer - YOLOv8 ve diğer AI modelleri ile görüntü analizi
"""
import asyncio
import time
import os
from typing import Optional, Dict, Any, List, Tuple
import numpy as np
import cv2
from dataclasses import dataclass, asdict
import torch

try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    print("Warning: ultralytics not available. AI analysis will be disabled.")


@dataclass
class Detection:
    """Tespit edilen nesne bilgisi"""
    class_id: int
    class_name: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # (x1, y1, x2, y2)
    center: Tuple[int, int]  # (cx, cy)


@dataclass
class AnalysisResult:
    """AI analiz sonucu"""
    detections: List[Detection]
    processing_time: float
    frame_size: Tuple[int, int]
    model_name: str
    confidence_threshold: float


@dataclass
class AIStats:
    """AI istatistikleri"""
    frames_analyzed: int = 0
    total_detections: int = 0
    avg_processing_time: float = 0.0
    avg_detections_per_frame: float = 0.0
    last_processing_time: float = 0.0


class AIAnalyzer:
    """
    YOLOv8 ve diğer AI modelleri ile görüntü analizi sınıfı
    """
    
    def __init__(self, model_path: str = "yolov8n.pt", 
                 confidence_threshold: float = 0.5,
                 iou_threshold: float = 0.45,
                 device: str = "auto"):
        """
        Args:
            model_path: Model dosya yolu
            confidence_threshold: Güven eşiği
            iou_threshold: IoU eşiği
            device: Cihaz ("cpu", "cuda", "auto")
        """
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        self.iou_threshold = iou_threshold
        self.device = device
        self.model = None
        self.is_initialized = False
        self.stats = AIStats()
        self._processing_times = []
        
        # COCO class names (YOLOv8 için)
        self.class_names = [
            'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
            'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench',
            'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
            'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
            'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove',
            'skateboard', 'surfboard', 'tennis racket', 'bottle', 'wine glass', 'cup',
            'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
            'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
            'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse',
            'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
            'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
            'toothbrush'
        ]
    
    async def initialize(self) -> bool:
        """AI modelini başlat"""
        if not YOLO_AVAILABLE:
            print("YOLO not available, AI analysis disabled")
            return False
        
        try:
            # Device ayarla
            if self.device == "auto":
                self.device = "cuda" if torch.cuda.is_available() else "cpu"
            
            # Model yükle
            if os.path.exists(self.model_path):
                self.model = YOLO(self.model_path)
            else:
                # Varsayılan model indir
                print(f"Model file not found: {self.model_path}")
                print("Downloading default YOLOv8 nano model...")
                self.model = YOLO("yolov8n.pt")
            
            # Model'i device'a taşı
            self.model.to(self.device)
            
            self.is_initialized = True
            print(f"AI Analyzer initialized with device: {self.device}")
            return True

        except Exception as e:
            print(f"AI Analyzer initialization failed: {e}")
            return False

    async def analyze_frame(self, frame: np.ndarray) -> Optional[AnalysisResult]:
        """
        Frame'i analiz et

        Args:
            frame: Analiz edilecek frame

        Returns:
            AnalysisResult: Analiz sonucu veya None
        """
        if not self.is_initialized or self.model is None:
            return None

        start_time = time.time()

        try:
            # YOLO inference
            results = self.model(frame,
                               conf=self.confidence_threshold,
                               iou=self.iou_threshold,
                               verbose=False)

            # Sonuçları parse et
            detections = []
            if results and len(results) > 0:
                result = results[0]
                if result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()
                    confidences = result.boxes.conf.cpu().numpy()
                    class_ids = result.boxes.cls.cpu().numpy().astype(int)

                    for i, (box, conf, class_id) in enumerate(zip(boxes, confidences, class_ids)):
                        x1, y1, x2, y2 = map(int, box)
                        cx = (x1 + x2) // 2
                        cy = (y1 + y2) // 2

                        class_name = self.class_names[class_id] if class_id < len(self.class_names) else f"class_{class_id}"

                        detection = Detection(
                            class_id=class_id,
                            class_name=class_name,
                            confidence=float(conf),
                            bbox=(x1, y1, x2, y2),
                            center=(cx, cy)
                        )
                        detections.append(detection)

            processing_time = time.time() - start_time

            # İstatistikleri güncelle
            self._update_stats(processing_time, len(detections))

            # Sonuç oluştur
            analysis_result = AnalysisResult(
                detections=detections,
                processing_time=processing_time,
                frame_size=(frame.shape[1], frame.shape[0]),
                model_name=self.model_path,
                confidence_threshold=self.confidence_threshold
            )

            return analysis_result

        except Exception as e:
            print(f"AI analysis error: {e}")
            return None

    async def draw_detections(self, frame: np.ndarray,
                            analysis_result: AnalysisResult) -> np.ndarray:
        """
        Tespit edilen nesneleri frame üzerine çiz

        Args:
            frame: Orijinal frame
            analysis_result: AI analiz sonucu

        Returns:
            np.ndarray: Çizimli frame
        """
        if not analysis_result or not analysis_result.detections:
            return frame

        result_frame = frame.copy()

        for detection in analysis_result.detections:
            x1, y1, x2, y2 = detection.bbox

            # Bounding box çiz
            color = self._get_class_color(detection.class_id)
            cv2.rectangle(result_frame, (x1, y1), (x2, y2), color, 2)

            # Label oluştur
            label = f"{detection.class_name}: {detection.confidence:.2f}"

            # Label background
            (label_width, label_height), _ = cv2.getTextSize(
                label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1
            )
            cv2.rectangle(result_frame, (x1, y1 - label_height - 10),
                         (x1 + label_width, y1), color, -1)

            # Label text
            cv2.putText(result_frame, label, (x1, y1 - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            # Center point
            cv2.circle(result_frame, detection.center, 3, color, -1)

        return result_frame

    def _get_class_color(self, class_id: int) -> Tuple[int, int, int]:
        """Sınıf ID'sine göre renk döndür"""
        colors = [
            (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),
            (255, 0, 255), (0, 255, 255), (128, 0, 0), (0, 128, 0),
            (0, 0, 128), (128, 128, 0), (128, 0, 128), (0, 128, 128)
        ]
        return colors[class_id % len(colors)]

    def _update_stats(self, processing_time: float, detection_count: int) -> None:
        """İstatistikleri güncelle"""
        self.stats.frames_analyzed += 1
        self.stats.total_detections += detection_count
        self.stats.last_processing_time = processing_time

        # Processing time tracking
        self._processing_times.append(processing_time)
        if len(self._processing_times) > 100:
            self._processing_times.pop(0)

        self.stats.avg_processing_time = sum(self._processing_times) / len(self._processing_times)

        # Average detections per frame
        if self.stats.frames_analyzed > 0:
            self.stats.avg_detections_per_frame = (
                self.stats.total_detections / self.stats.frames_analyzed
            )

    def get_stats(self) -> Dict[str, Any]:
        """İstatistikleri döndür"""
        stats_dict = asdict(self.stats)
        stats_dict.update({
            "model_path": self.model_path,
            "device": self.device,
            "confidence_threshold": self.confidence_threshold,
            "iou_threshold": self.iou_threshold,
            "is_initialized": self.is_initialized,
            "avg_processing_time_ms": round(self.stats.avg_processing_time * 1000, 2),
            "yolo_available": YOLO_AVAILABLE
        })
        return stats_dict

    def set_confidence_threshold(self, threshold: float) -> None:
        """Güven eşiğini ayarla"""
        self.confidence_threshold = max(0.0, min(1.0, threshold))

    def set_iou_threshold(self, threshold: float) -> None:
        """IoU eşiğini ayarla"""
        self.iou_threshold = max(0.0, min(1.0, threshold))

    def reset_stats(self) -> None:
        """İstatistikleri sıfırla"""
        self.stats = AIStats()
        self._processing_times = []

    async def cleanup(self) -> None:
        """Kaynakları temizle"""
        if self.model is not None:
            del self.model
            self.model = None
        self.is_initialized = False

        # GPU memory temizle
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
