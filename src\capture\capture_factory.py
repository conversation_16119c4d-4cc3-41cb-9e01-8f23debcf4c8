"""
Capture Factory - Yakalama sınıfları için factory pattern
"""
from typing import Optional, Dict, Any, Union
from .base_capture import BaseCapture
from .screen_capture import ScreenCapture
from .webcam_capture import WebcamCapture


class CaptureFactory:
    """
    Yakalama sınıfları için factory sınıfı
    """
    
    CAPTURE_TYPES = {
        "screen": ScreenCapture,
        "webcam": WebcamCapture
    }
    
    @classmethod
    def create_capture(cls, capture_type: str, **kwargs) -> Optional[BaseCapture]:
        """
        Yakalama sınıfı oluştur
        
        Args:
            capture_type: "screen" veya "webcam"
            **kwargs: Yakalama sınıfına özel parametreler
            
        Returns:
            BaseCapture: Yakalama sınıfı instance'ı veya None
        """
        if capture_type not in cls.CAPTURE_TYPES:
            print(f"Unsupported capture type: {capture_type}")
            return None
        
        try:
            capture_class = cls.CAPTURE_TYPES[capture_type]
            return capture_class(**kwargs)
        except Exception as e:
            print(f"Failed to create {capture_type} capture: {e}")
            return None
    
    @classmethod
    def create_screen_capture(cls, width: int = 1280, height: int = 720, 
                            fps: int = 30, monitor: int = 1, 
                            region: Optional[Dict[str, int]] = None) -> Optional[ScreenCapture]:
        """
        Ekran yakalama sınıfı oluştur
        
        Args:
            width: Genişlik
            height: Yükseklik
            fps: FPS
            monitor: Monitör numarası
            region: Yakalama bölgesi
            
        Returns:
            ScreenCapture: Ekran yakalama instance'ı veya None
        """
        return cls.create_capture(
            "screen",
            width=width,
            height=height,
            fps=fps,
            monitor=monitor,
            region=region
        )
    
    @classmethod
    def create_webcam_capture(cls, width: int = 1280, height: int = 720,
                            fps: int = 30, camera_index: int = 0) -> Optional[WebcamCapture]:
        """
        Webcam yakalama sınıfı oluştur
        
        Args:
            width: Genişlik
            height: Yükseklik
            fps: FPS
            camera_index: Kamera indeksi
            
        Returns:
            WebcamCapture: Webcam yakalama instance'ı veya None
        """
        return cls.create_capture(
            "webcam",
            width=width,
            height=height,
            fps=fps,
            camera_index=camera_index
        )
    
    @classmethod
    def get_available_capture_types(cls) -> list:
        """Mevcut yakalama türlerini listele"""
        return list(cls.CAPTURE_TYPES.keys())
    
    @classmethod
    def get_system_info(cls) -> Dict[str, Any]:
        """Sistem yakalama bilgilerini al"""
        info = {
            "available_capture_types": cls.get_available_capture_types(),
            "screen_monitors": [],
            "webcam_cameras": []
        }
        
        # Ekran monitörlerini kontrol et
        try:
            temp_screen = ScreenCapture()
            if await temp_screen.initialize():
                info["screen_monitors"] = temp_screen.get_available_monitors()
                await temp_screen.cleanup()
        except Exception as e:
            print(f"Failed to get screen info: {e}")
        
        # Webcam kameralarını kontrol et
        try:
            info["webcam_cameras"] = WebcamCapture.get_available_cameras()
        except Exception as e:
            print(f"Failed to get webcam info: {e}")
        
        return info
    
    @classmethod
    def validate_capture_config(cls, capture_type: str, config: Dict[str, Any]) -> bool:
        """
        Yakalama konfigürasyonunu doğrula
        
        Args:
            capture_type: Yakalama türü
            config: Konfigürasyon parametreleri
            
        Returns:
            bool: Konfigürasyon geçerli ise True
        """
        if capture_type not in cls.CAPTURE_TYPES:
            return False
        
        # Temel parametreleri kontrol et
        required_params = ["width", "height", "fps"]
        for param in required_params:
            if param not in config:
                return False
            if not isinstance(config[param], int) or config[param] <= 0:
                return False
        
        # Yakalama türüne özel kontroller
        if capture_type == "screen":
            if "monitor" in config:
                if not isinstance(config["monitor"], int) or config["monitor"] < 0:
                    return False
        
        elif capture_type == "webcam":
            if "camera_index" in config:
                if not isinstance(config["camera_index"], int) or config["camera_index"] < 0:
                    return False
        
        return True
