/**
 * Main Application - Tüm bileşenleri koordine eder
 */

class AIStreamApp {
    constructor() {
        this.isInitialized = false;
        this.isStreaming = false;
        this.aiEnabled = true;
        this.confidenceThreshold = 0.5;
        
        // Subscriptions
        this.subscriptions = {
            video_stream: true,
            ai_analysis: true,
            system_stats: false
        };
        
        // Performance tracking
        this.performance = {
            startTime: null,
            frameCount: 0,
            lastFpsUpdate: 0
        };
        
        this.init();
    }
    
    async init() {
        try {
            console.log('Initializing AI-RealTimeStream App...');
            
            // Setup event handlers
            this.setupEventHandlers();
            
            // Setup WebSocket handlers
            this.setupWebSocketHandlers();
            
            // Setup component callbacks
            this.setupComponentCallbacks();
            
            // Initialize modal
            this.setupModal();
            
            this.isInitialized = true;
            console.log('App initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize app:', error);
        }
    }
    
    setupEventHandlers() {
        // Connection controls
        document.getElementById('connectBtn')?.addEventListener('click', () => {
            this.connect();
        });
        
        document.getElementById('disconnectBtn')?.addEventListener('click', () => {
            this.disconnect();
        });
        
        // Stream controls
        document.getElementById('startStreamBtn')?.addEventListener('click', () => {
            this.startStream();
        });
        
        document.getElementById('stopStreamBtn')?.addEventListener('click', () => {
            this.stopStream();
        });
        
        // AI controls
        document.getElementById('enableAI')?.addEventListener('change', (e) => {
            this.aiEnabled = e.target.checked;
            console.log('AI analysis:', this.aiEnabled ? 'enabled' : 'disabled');
        });
        
        document.getElementById('confidenceSlider')?.addEventListener('input', (e) => {
            this.confidenceThreshold = parseFloat(e.target.value);
            document.getElementById('confidenceValue').textContent = this.confidenceThreshold;
            
            // Apply filter to current detections
            if (window.aiVisualizer) {
                window.aiVisualizer.filterByConfidence(this.confidenceThreshold);
            }
        });
        
        // Subscription controls
        document.getElementById('subscribeVideo')?.addEventListener('change', (e) => {
            this.toggleSubscription('video_stream', e.target.checked);
        });
        
        document.getElementById('subscribeAI')?.addEventListener('change', (e) => {
            this.toggleSubscription('ai_analysis', e.target.checked);
        });
        
        document.getElementById('subscribeStats')?.addEventListener('change', (e) => {
            this.toggleSubscription('system_stats', e.target.checked);
        });
        
        // About modal
        document.getElementById('aboutBtn')?.addEventListener('click', () => {
            this.showModal();
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }
    
    setupWebSocketHandlers() {
        // Connection events
        window.wsClient.onConnect = () => {
            this.onWebSocketConnect();
        };
        
        window.wsClient.onDisconnect = () => {
            this.onWebSocketDisconnect();
        };
        
        window.wsClient.onError = (error) => {
            console.error('WebSocket error:', error);
        };
        
        // Message handlers
        window.wsClient.registerHandler('video_frame', (data) => {
            this.handleVideoFrame(data);
        });
        
        window.wsClient.registerHandler('ai_analysis', (data) => {
            this.handleAIAnalysis(data);
        });
        
        window.wsClient.registerHandler('system_stats', (data) => {
            this.handleSystemStats(data);
        });
        
        window.wsClient.registerHandler('stats_response', (data) => {
            window.statsMonitor.updateStats(data.stats);
        });
    }
    
    setupComponentCallbacks() {
        // Video renderer callbacks
        window.videoRenderer.onFrameRendered = (frameInfo) => {
            this.performance.frameCount++;
        };
        
        // AI visualizer callbacks
        window.aiVisualizer.onDetectionUpdate = (detections) => {
            // Update detection count in real-time
        };
        
        // Stats monitor callbacks
        window.statsMonitor.onStatsUpdate = (stats) => {
            // Handle stats updates
        };
    }
    
    setupModal() {
        const modal = document.getElementById('aboutModal');
        const closeBtn = modal?.querySelector('.close');
        
        closeBtn?.addEventListener('click', () => {
            this.hideModal();
        });
        
        window.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hideModal();
            }
        });
    }
    
    async connect() {
        try {
            this.updateButtonStates('connecting');
            await window.wsClient.connect();
        } catch (error) {
            console.error('Connection failed:', error);
            this.updateButtonStates('disconnected');
        }
    }
    
    disconnect() {
        window.wsClient.disconnect();
        this.stopStream();
    }
    
    onWebSocketConnect() {
        console.log('Connected to server');
        this.updateButtonStates('connected');
        
        // Subscribe to enabled channels
        Object.entries(this.subscriptions).forEach(([channel, enabled]) => {
            if (enabled) {
                window.wsClient.subscribe(channel);
            }
        });
        
        // Start stats monitoring
        window.statsMonitor.startMonitoring();
    }
    
    onWebSocketDisconnect() {
        console.log('Disconnected from server');
        this.updateButtonStates('disconnected');
        this.stopStream();
        window.statsMonitor.stopMonitoring();
    }
    
    startStream() {
        if (!window.wsClient.isConnected) {
            alert('Önce sunucuya bağlanın!');
            return;
        }
        
        this.isStreaming = true;
        this.performance.startTime = Date.now();
        this.performance.frameCount = 0;
        
        window.videoRenderer.startRendering();
        this.updateButtonStates('streaming');
        
        // Send start stream command
        window.wsClient.send({
            type: 'start_stream',
            ai_enabled: this.aiEnabled,
            confidence_threshold: this.confidenceThreshold
        });
        
        console.log('Stream started');
    }
    
    stopStream() {
        if (!this.isStreaming) {
            return;
        }
        
        this.isStreaming = false;
        window.videoRenderer.stopRendering();
        
        // Send stop stream command
        if (window.wsClient.isConnected) {
            window.wsClient.send({
                type: 'stop_stream'
            });
        }
        
        this.updateButtonStates('connected');
        console.log('Stream stopped');
    }
    
    handleVideoFrame(data) {
        if (!this.isStreaming || !this.subscriptions.video_stream) {
            return;
        }
        
        window.videoRenderer.renderFrame(data.data, data.metadata);
    }
    
    handleAIAnalysis(data) {
        if (!this.aiEnabled || !this.subscriptions.ai_analysis) {
            return;
        }
        
        // Filter detections by confidence threshold
        const filteredDetections = data.detections.filter(
            detection => detection.confidence >= this.confidenceThreshold
        );
        
        const analysisResult = {
            ...data,
            detections: filteredDetections
        };
        
        window.aiVisualizer.updateDetections(analysisResult);
    }
    
    handleSystemStats(data) {
        if (!this.subscriptions.system_stats) {
            return;
        }
        
        window.statsMonitor.updateStats(data.stats);
    }
    
    toggleSubscription(channel, enabled) {
        this.subscriptions[channel] = enabled;
        
        if (window.wsClient.isConnected) {
            if (enabled) {
                window.wsClient.subscribe(channel);
            } else {
                window.wsClient.unsubscribe(channel);
            }
        }
        
        console.log(`Subscription ${channel}:`, enabled ? 'enabled' : 'disabled');
    }
    
    updateButtonStates(state) {
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const startStreamBtn = document.getElementById('startStreamBtn');
        const stopStreamBtn = document.getElementById('stopStreamBtn');
        
        switch (state) {
            case 'disconnected':
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                startStreamBtn.disabled = true;
                stopStreamBtn.disabled = true;
                break;
                
            case 'connecting':
                connectBtn.disabled = true;
                disconnectBtn.disabled = true;
                startStreamBtn.disabled = true;
                stopStreamBtn.disabled = true;
                break;
                
            case 'connected':
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                startStreamBtn.disabled = false;
                stopStreamBtn.disabled = true;
                break;
                
            case 'streaming':
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                startStreamBtn.disabled = true;
                stopStreamBtn.disabled = false;
                break;
        }
    }
    
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + key combinations
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'Enter':
                    e.preventDefault();
                    if (window.wsClient.isConnected) {
                        this.isStreaming ? this.stopStream() : this.startStream();
                    } else {
                        this.connect();
                    }
                    break;
                    
                case 's':
                    e.preventDefault();
                    if (window.videoRenderer.takeScreenshot) {
                        window.videoRenderer.downloadScreenshot();
                    }
                    break;
                    
                case 'r':
                    e.preventDefault();
                    window.statsMonitor.requestStats();
                    break;
            }
        }
        
        // Escape key
        if (e.key === 'Escape') {
            this.hideModal();
        }
    }
    
    showModal() {
        const modal = document.getElementById('aboutModal');
        if (modal) {
            modal.style.display = 'block';
        }
    }
    
    hideModal() {
        const modal = document.getElementById('aboutModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }
    
    getAppStats() {
        return {
            isInitialized: this.isInitialized,
            isStreaming: this.isStreaming,
            aiEnabled: this.aiEnabled,
            confidenceThreshold: this.confidenceThreshold,
            subscriptions: this.subscriptions,
            performance: this.performance,
            uptime: this.performance.startTime ? Date.now() - this.performance.startTime : 0
        };
    }
    
    exportConfiguration() {
        return {
            timestamp: Date.now(),
            version: '1.0.0',
            settings: {
                aiEnabled: this.aiEnabled,
                confidenceThreshold: this.confidenceThreshold,
                subscriptions: this.subscriptions
            },
            stats: this.getAppStats()
        };
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.aiStreamApp = new AIStreamApp();
    
    // Global error handler
    window.addEventListener('error', (e) => {
        console.error('Global error:', e.error);
    });
    
    // Unload handler
    window.addEventListener('beforeunload', () => {
        if (window.wsClient && window.wsClient.isConnected) {
            window.wsClient.disconnect();
        }
    });
    
    console.log('AI-RealTimeStream loaded successfully!');
});
