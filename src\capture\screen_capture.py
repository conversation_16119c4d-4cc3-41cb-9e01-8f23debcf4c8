"""
Screen Capture Module - MSS kullanarak ekran yakalama
"""
import asyncio
import time
from typing import Optional, Dict, Any, Tuple
import numpy as np
import cv2
from PIL import Image
import mss
import mss.tools

from .base_capture import BaseCapture


class ScreenCapture(BaseCapture):
    """
    MSS (Multiple Screen Shot) kullanarak ekran yakalama sınıfı
    """
    
    def __init__(self, width: int = 1280, height: int = 720, fps: int = 30, 
                 monitor: int = 1, region: Optional[Dict[str, int]] = None):
        """
        Args:
            width: Hedef genişlik
            height: Hedef yükseklik  
            fps: Hedef FPS
            monitor: Monitör numarası (1-based, 0 = tüm monitörler)
            region: Yakalama bölgesi {"top": y, "left": x, "width": w, "height": h}
        """
        super().__init__(width, height, fps)
        self.monitor = monitor
        self.region = region
        self.sct = None
        self.monitor_info = None
        
    async def initialize(self) -> bool:
        """MSS'i başlat ve monitör bilgilerini al"""
        try:
            self.sct = mss.mss()
            
            # Monitör bilgilerini al
            if self.monitor == 0:
                # Tüm monitörler
                self.monitor_info = self.sct.monitors[0]
            else:
                # Belirli monitör
                if self.monitor <= len(self.sct.monitors) - 1:
                    self.monitor_info = self.sct.monitors[self.monitor]
                else:
                    # Varsayılan olarak ilk monitörü kullan
                    self.monitor_info = self.sct.monitors[1]
            
            # Özel bölge belirtilmişse kullan
            if self.region:
                self.monitor_info.update(self.region)
            
            print(f"Screen capture initialized: {self.monitor_info}")
            return True
            
        except Exception as e:
            print(f"Screen capture initialization failed: {e}")
            return False
    
    async def capture_frame(self) -> Optional[np.ndarray]:
        """Ekran görüntüsü yakala"""
        if not self.sct or not self.monitor_info:
            return None
        
        try:
            # Ekran görüntüsü yakala
            screenshot = self.sct.grab(self.monitor_info)
            
            # PIL Image'e çevir
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            
            # NumPy array'e çevir
            frame = np.array(img)
            
            # BGR formatına çevir (OpenCV uyumluluğu için)
            frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            
            # Boyutlandır
            if frame.shape[:2] != (self.height, self.width):
                frame = cv2.resize(frame, (self.width, self.height))
            
            return frame
            
        except Exception as e:
            print(f"Screen capture error: {e}")
            return None
    
    async def cleanup(self) -> None:
        """Kaynakları temizle"""
        if self.sct:
            self.sct.close()
            self.sct = None
    
    def get_available_monitors(self) -> list:
        """Mevcut monitörleri listele"""
        if not self.sct:
            temp_sct = mss.mss()
            monitors = temp_sct.monitors
            temp_sct.close()
        else:
            monitors = self.sct.monitors
        
        return [
            {
                "index": i,
                "info": monitor,
                "resolution": f"{monitor['width']}x{monitor['height']}"
            }
            for i, monitor in enumerate(monitors)
        ]
    
    def set_monitor(self, monitor: int) -> None:
        """Monitör değiştir"""
        self.monitor = monitor
        if self.sct:
            # Yeniden başlat
            asyncio.create_task(self.initialize())
    
    def set_region(self, region: Optional[Dict[str, int]]) -> None:
        """Yakalama bölgesi ayarla"""
        self.region = region
        if self.sct:
            # Monitör bilgilerini güncelle
            if self.monitor == 0:
                self.monitor_info = self.sct.monitors[0].copy()
            else:
                monitor_idx = min(self.monitor, len(self.sct.monitors) - 1)
                self.monitor_info = self.sct.monitors[monitor_idx].copy()
            
            if self.region:
                self.monitor_info.update(self.region)
    
    def get_monitor_info(self) -> Optional[Dict[str, Any]]:
        """Aktif monitör bilgilerini döndür"""
        return self.monitor_info
    
    async def capture_to_file(self, filename: str) -> bool:
        """Ekran görüntüsünü dosyaya kaydet"""
        frame = await self.capture_frame()
        if frame is not None:
            try:
                cv2.imwrite(filename, frame)
                return True
            except Exception as e:
                print(f"Failed to save screenshot: {e}")
                return False
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Genişletilmiş istatistikler"""
        stats = super().get_stats()
        stats.update({
            "monitor": self.monitor,
            "monitor_info": self.monitor_info,
            "region": self.region,
            "capture_type": "screen"
        })
        return stats
