"""
Message Handler - WebSocket mesajlarını işleme
"""
import asyncio
import json
import time
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class MessageTemplate:
    """Mesaj şablonu"""
    type: str
    required_fields: List[str]
    optional_fields: List[str] = None
    
    def __post_init__(self):
        if self.optional_fields is None:
            self.optional_fields = []


class MessageHandler:
    """
    WebSocket mesajlarını işleyen sınıf
    """
    
    def __init__(self):
        self.handlers: Dict[str, Callable] = {}
        self.message_templates: Dict[str, MessageTemplate] = {}
        self.stats = {
            "messages_processed": 0,
            "messages_failed": 0,
            "handler_calls": {},
            "avg_processing_time": 0.0
        }
        self._processing_times = []
        
        # Varsayılan mesaj şablonları
        self._setup_default_templates()
    
    def _setup_default_templates(self) -> None:
        """Varsayılan mesaj şablonlarını ayarla"""
        templates = [
            MessageTemplate("ping", []),
            MessageTemplate("pong", []),
            MessageTemplate("subscribe", ["channel"]),
            MessageTemplate("unsubscribe", ["channel"]),
            MessageTemplate("get_stats", []),
            MessageTemplate("video_frame", ["data"], ["timestamp", "metadata"]),
            MessageTemplate("ai_analysis", ["detections"], ["timestamp", "processing_time"]),
            MessageTemplate("system_stats", ["stats"], ["timestamp"]),
            MessageTemplate("error", ["message"], ["code", "details"]),
            MessageTemplate("connection_established", ["client_id"], ["server_time"]),
            MessageTemplate("subscription_response", ["channel", "success"]),
            MessageTemplate("server_shutdown", ["timestamp"])
        ]
        
        for template in templates:
            self.message_templates[template.type] = template
    
    def register_handler(self, message_type: str, handler: Callable) -> None:
        """
        Mesaj türü için handler kaydet
        
        Args:
            message_type: Mesaj türü
            handler: Handler fonksiyonu
        """
        self.handlers[message_type] = handler
        self.stats["handler_calls"][message_type] = 0
        logger.info(f"Registered handler for message type: {message_type}")
    
    def register_template(self, template: MessageTemplate) -> None:
        """
        Yeni mesaj şablonu kaydet
        
        Args:
            template: Mesaj şablonu
        """
        self.message_templates[template.type] = template
        logger.info(f"Registered message template: {template.type}")
    
    async def process_message(self, client_id: str, message: str) -> Optional[Dict[str, Any]]:
        """
        Mesajı işle
        
        Args:
            client_id: İstemci ID'si
            message: Gelen mesaj
            
        Returns:
            Optional[Dict]: Yanıt mesajı veya None
        """
        start_time = time.time()
        
        try:
            # JSON parse
            try:
                data = json.loads(message)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON from {client_id}: {e}")
                self.stats["messages_failed"] += 1
                return self._create_error_message("Invalid JSON format", "JSON_DECODE_ERROR")
            
            # Mesaj türünü al
            message_type = data.get("type")
            if not message_type:
                logger.error(f"Missing message type from {client_id}")
                self.stats["messages_failed"] += 1
                return self._create_error_message("Missing message type", "MISSING_TYPE")
            
            # Mesaj şablonunu kontrol et
            if message_type in self.message_templates:
                validation_result = self._validate_message(data, message_type)
                if validation_result:
                    self.stats["messages_failed"] += 1
                    return validation_result
            
            # Handler'ı çağır
            response = None
            if message_type in self.handlers:
                try:
                    response = await self.handlers[message_type](client_id, data)
                    self.stats["handler_calls"][message_type] += 1
                except Exception as e:
                    logger.error(f"Handler error for {message_type} from {client_id}: {e}")
                    self.stats["messages_failed"] += 1
                    return self._create_error_message(f"Handler error: {str(e)}", "HANDLER_ERROR")
            else:
                logger.warning(f"No handler for message type: {message_type} from {client_id}")
                response = self._create_error_message(f"Unknown message type: {message_type}", "UNKNOWN_TYPE")
            
            # İstatistikleri güncelle
            processing_time = time.time() - start_time
            self._update_stats(processing_time)
            
            return response
            
        except Exception as e:
            logger.error(f"Unexpected error processing message from {client_id}: {e}")
            self.stats["messages_failed"] += 1
            return self._create_error_message("Internal server error", "INTERNAL_ERROR")
    
    def _validate_message(self, data: Dict[str, Any], message_type: str) -> Optional[Dict[str, Any]]:
        """
        Mesajı şablona göre doğrula
        
        Args:
            data: Mesaj verisi
            message_type: Mesaj türü
            
        Returns:
            Optional[Dict]: Hata mesajı veya None
        """
        template = self.message_templates[message_type]
        
        # Gerekli alanları kontrol et
        for field in template.required_fields:
            if field not in data:
                return self._create_error_message(
                    f"Missing required field: {field}",
                    "MISSING_FIELD",
                    {"field": field, "message_type": message_type}
                )
        
        return None
    
    def _create_error_message(self, message: str, code: str, 
                            details: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Hata mesajı oluştur
        
        Args:
            message: Hata mesajı
            code: Hata kodu
            details: Ek detaylar
            
        Returns:
            Dict: Hata mesajı
        """
        error_msg = {
            "type": "error",
            "message": message,
            "code": code,
            "timestamp": time.time()
        }
        
        if details:
            error_msg["details"] = details
        
        return error_msg
    
    def _update_stats(self, processing_time: float) -> None:
        """İstatistikleri güncelle"""
        self.stats["messages_processed"] += 1
        
        # Processing time tracking
        self._processing_times.append(processing_time)
        if len(self._processing_times) > 1000:
            self._processing_times.pop(0)
        
        self.stats["avg_processing_time"] = sum(self._processing_times) / len(self._processing_times)
    
    def create_video_frame_message(self, frame_data: str, timestamp: Optional[float] = None,
                                 metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Video frame mesajı oluştur
        
        Args:
            frame_data: Base64 encoded frame data
            timestamp: Zaman damgası
            metadata: Ek metadata
            
        Returns:
            Dict: Video frame mesajı
        """
        message = {
            "type": "video_frame",
            "data": frame_data,
            "timestamp": timestamp or time.time()
        }
        
        if metadata:
            message["metadata"] = metadata
        
        return message
    
    def create_ai_analysis_message(self, detections: List[Dict[str, Any]], 
                                 processing_time: float,
                                 timestamp: Optional[float] = None) -> Dict[str, Any]:
        """
        AI analiz mesajı oluştur
        
        Args:
            detections: Tespit edilen nesneler
            processing_time: İşleme süresi
            timestamp: Zaman damgası
            
        Returns:
            Dict: AI analiz mesajı
        """
        return {
            "type": "ai_analysis",
            "detections": detections,
            "processing_time": processing_time,
            "timestamp": timestamp or time.time()
        }
    
    def create_system_stats_message(self, stats: Dict[str, Any],
                                  timestamp: Optional[float] = None) -> Dict[str, Any]:
        """
        Sistem istatistikleri mesajı oluştur
        
        Args:
            stats: Sistem istatistikleri
            timestamp: Zaman damgası
            
        Returns:
            Dict: Sistem istatistikleri mesajı
        """
        return {
            "type": "system_stats",
            "stats": stats,
            "timestamp": timestamp or time.time()
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """Handler istatistiklerini döndür"""
        return {
            **self.stats,
            "avg_processing_time_ms": round(self.stats["avg_processing_time"] * 1000, 2),
            "registered_handlers": list(self.handlers.keys()),
            "registered_templates": list(self.message_templates.keys())
        }
    
    def reset_stats(self) -> None:
        """İstatistikleri sıfırla"""
        self.stats = {
            "messages_processed": 0,
            "messages_failed": 0,
            "handler_calls": {msg_type: 0 for msg_type in self.handlers.keys()},
            "avg_processing_time": 0.0
        }
        self._processing_times = []
