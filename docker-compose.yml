# AI-RealTimeStream Docker Compose Configuration
version: '3.8'

services:
  # Main application service
  ai-realtime-stream:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: ai-realtime-stream
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=false
      - LOG_LEVEL=INFO
      - ENABLE_AI_PROCESSING=true
      - CAPTURE_SOURCE=screen
      - CAPTURE_FPS=30
      - CAPTURE_WIDTH=1280
      - CAPTURE_HEIGHT=720
    volumes:
      # Persistent data
      - ./models:/app/models
      - ./logs:/app/logs
      - ./cache:/app/cache
      # Configuration
      - ./.env:/app/.env:ro
      # X11 forwarding for screen capture (Linux)
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - /dev/shm:/dev/shm
    environment:
      - DISPLAY=${DISPLAY:-:0}
    devices:
      # GPU access (if available)
      - /dev/dri:/dev/dri
    networks:
      - ai-stream-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: ai-stream-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - ai-stream-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: ai-stream-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - ai-realtime-stream
    networks:
      - ai-stream-network
    profiles:
      - production

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-stream-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ai-stream-network
    profiles:
      - monitoring

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: ai-stream-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - ai-stream-network
    profiles:
      - monitoring

# Development override
  ai-realtime-stream-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: ai-realtime-stream-dev
    restart: "no"
    ports:
      - "8000:8000"
      - "8888:8888"  # Jupyter
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - ENABLE_AI_PROCESSING=true
    volumes:
      # Mount source code for development
      - .:/app
      - ./models:/app/models
      - ./logs:/app/logs
      # X11 forwarding
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - /dev/shm:/dev/shm
    environment:
      - DISPLAY=${DISPLAY:-:0}
    devices:
      - /dev/dri:/dev/dri
    networks:
      - ai-stream-network
    profiles:
      - development
    command: ["python", "src/main.py"]

# Networks
networks:
  ai-stream-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  nginx_logs:
    driver: local
