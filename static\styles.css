/* AI-RealTimeStream Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    color: #4a5568;
    font-size: 2rem;
    font-weight: 700;
}

.header h1 i {
    color: #667eea;
    margin-right: 10px;
}

.connection-status {
    display: flex;
    align-items: center;
    font-weight: 600;
}

.status-connected {
    color: #48bb78;
}

.status-disconnected {
    color: #f56565;
}

.status-connecting {
    color: #ed8936;
}

/* Main Content */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 300px 1fr 350px;
    grid-template-rows: 1fr auto;
    gap: 20px;
    grid-template-areas: 
        "control video analysis"
        "stats stats stats";
}

/* Control Panel */
.control-panel {
    grid-area: control;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    height: fit-content;
}

.panel-section h3 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.control-group {
    margin-bottom: 25px;
}

.control-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2d3748;
}

.button-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn {
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #5a67d8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #a0aec0;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #718096;
    transform: translateY(-2px);
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #38a169;
    transform: translateY(-2px);
}

.btn-danger {
    background: #f56565;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #e53e3e;
    transform: translateY(-2px);
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* Checkbox Styles */
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: #e2e8f0;
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

/* Slider Styles */
.slider-group {
    margin-top: 10px;
}

.slider-group label {
    font-size: 0.9rem;
    margin-bottom: 5px;
}

input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e2e8f0;
    outline: none;
    -webkit-appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
}

/* Video Container */
.video-container {
    grid-area: video;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.video-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.video-header h3 {
    color: #4a5568;
    font-size: 1.2rem;
}

.video-info {
    display: flex;
    gap: 15px;
    font-size: 0.9rem;
    color: #718096;
}

.video-display {
    position: relative;
    background: #000;
    border-radius: 10px;
    overflow: hidden;
    aspect-ratio: 16/9;
}

#videoCanvas {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.8);
    color: white;
}

.loading-spinner {
    text-align: center;
}

.loading-spinner i {
    font-size: 2rem;
    margin-bottom: 10px;
}

/* Analysis Panel */
.analysis-panel {
    grid-area: analysis;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    height: fit-content;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.panel-header h3 {
    color: #4a5568;
    font-size: 1.2rem;
}

.analysis-stats {
    display: flex;
    gap: 15px;
    font-size: 0.9rem;
    color: #718096;
}

.detections-list {
    max-height: 400px;
    overflow-y: auto;
}

.detection-item {
    background: #f7fafc;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    border-left: 4px solid #667eea;
}

.detection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.detection-name {
    font-weight: 600;
    color: #2d3748;
}

.detection-confidence {
    background: #667eea;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.detection-position {
    font-size: 0.9rem;
    color: #718096;
}

.no-detections {
    text-align: center;
    color: #a0aec0;
    font-style: italic;
    padding: 20px;
}

/* Statistics Panel */
.stats-panel {
    grid-area: stats;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    background: #f7fafc;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.stat-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.stat-label {
    font-size: 0.9rem;
    color: #718096;
    margin-bottom: 8px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #718096;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #5a67d8;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 80%;
    max-width: 600px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
    color: #4a5568;
}

.close {
    color: #a0aec0;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #718096;
}

.modal-body {
    padding: 20px;
}

.modal-body h4 {
    color: #4a5568;
    margin: 15px 0 10px 0;
}

.modal-body ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.modal-body li {
    margin-bottom: 5px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-areas: 
            "control"
            "video"
            "analysis"
            "stats";
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .button-group {
        justify-content: center;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
