"""
AI-RealTimeStream Configuration Settings
"""
import os
from typing import Optional
from pydantic import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Server Configuration
    HOST: str = "localhost"
    PORT: int = 8000
    DEBUG: bool = True
    
    # WebSocket Configuration
    WS_MAX_CONNECTIONS: int = 100
    WS_PING_INTERVAL: int = 20
    WS_PING_TIMEOUT: int = 10
    
    # Capture Configuration
    CAPTURE_FPS: int = 30
    CAPTURE_WIDTH: int = 1280
    CAPTURE_HEIGHT: int = 720
    CAPTURE_SOURCE: str = "screen"  # "screen" or "webcam"
    WEBCAM_INDEX: int = 0
    
    # Processing Configuration
    PROCESSING_THREADS: int = 4
    MAX_QUEUE_SIZE: int = 100
    IMAGE_QUALITY: int = 85
    COMPRESSION_FORMAT: str = "JPEG"
    
    # AI Model Configuration
    AI_MODEL_PATH: str = "models/yolov8n.pt"
    AI_CONFIDENCE_THRESHOLD: float = 0.5
    AI_IOU_THRESHOLD: float = 0.45
    AI_MAX_DETECTIONS: int = 100
    ENABLE_AI_PROCESSING: bool = True
    
    # Performance Configuration
    BUFFER_SIZE: int = 1024 * 1024  # 1MB
    MAX_FRAME_BUFFER: int = 10
    ENABLE_FRAME_SKIP: bool = True
    TARGET_LATENCY_MS: int = 100
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FILE: Optional[str] = "logs/app.log"
    ENABLE_PERFORMANCE_LOGGING: bool = True
    
    # Security Configuration
    CORS_ORIGINS: list = ["*"]
    MAX_MESSAGE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


# Model configurations
YOLO_MODELS = {
    "nano": "yolov8n.pt",
    "small": "yolov8s.pt", 
    "medium": "yolov8m.pt",
    "large": "yolov8l.pt",
    "xlarge": "yolov8x.pt"
}

# Supported capture sources
CAPTURE_SOURCES = {
    "screen": "Screen capture using MSS",
    "webcam": "Webcam capture using OpenCV"
}

# Image processing presets
PROCESSING_PRESETS = {
    "low_latency": {
        "quality": 60,
        "fps": 60,
        "resolution": (640, 480)
    },
    "balanced": {
        "quality": 85,
        "fps": 30,
        "resolution": (1280, 720)
    },
    "high_quality": {
        "quality": 95,
        "fps": 15,
        "resolution": (1920, 1080)
    }
}
