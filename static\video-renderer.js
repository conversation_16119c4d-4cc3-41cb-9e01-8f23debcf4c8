/**
 * Video Renderer - Video stream'i canvas'a render eder
 */

class VideoRenderer {
    constructor(canvasId, overlayId) {
        this.canvas = document.getElementById(canvasId);
        this.overlay = document.getElementById(overlayId);
        this.ctx = this.canvas.getContext('2d');
        
        // Video properties
        this.currentFrame = null;
        this.frameCount = 0;
        this.lastFrameTime = 0;
        this.fps = 0;
        this.resolution = { width: 0, height: 0 };
        
        // Performance tracking
        this.frameBuffer = [];
        this.maxBufferSize = 30; // For FPS calculation
        this.renderStats = {
            framesRendered: 0,
            totalRenderTime: 0,
            avgRenderTime: 0,
            droppedFrames: 0
        };
        
        // Canvas settings
        this.setupCanvas();
        
        // Animation frame
        this.animationId = null;
        this.isRendering = false;
        
        // Event handlers
        this.onFrameRendered = null;
        this.onStatsUpdate = null;
    }
    
    setupCanvas() {
        // Set canvas size
        this.canvas.width = 1280;
        this.canvas.height = 720;
        
        // Set canvas style for responsive design
        this.canvas.style.width = '100%';
        this.canvas.style.height = '100%';
        this.canvas.style.objectFit = 'contain';
        
        // Set rendering context properties
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
        
        // Initial black background
        this.clearCanvas();
    }
    
    clearCanvas() {
        this.ctx.fillStyle = '#000000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    showOverlay(show = true) {
        if (this.overlay) {
            this.overlay.style.display = show ? 'flex' : 'none';
        }
    }
    
    updateOverlayMessage(message, showSpinner = true) {
        if (this.overlay) {
            const spinner = showSpinner ? '<i class="fas fa-spinner fa-spin"></i>' : '';
            this.overlay.innerHTML = `
                <div class="loading-spinner">
                    ${spinner}
                    <p>${message}</p>
                </div>
            `;
        }
    }
    
    renderFrame(frameData, metadata = {}) {
        if (!frameData) {
            console.warn('No frame data provided');
            return false;
        }
        
        const startTime = performance.now();
        
        try {
            // Create image from base64 data
            const img = new Image();
            
            img.onload = () => {
                // Hide overlay when first frame is rendered
                if (this.frameCount === 0) {
                    this.showOverlay(false);
                }
                
                // Calculate aspect ratio and positioning
                const { x, y, width, height } = this.calculateDrawDimensions(img.width, img.height);
                
                // Clear canvas
                this.clearCanvas();
                
                // Draw image
                this.ctx.drawImage(img, x, y, width, height);
                
                // Update frame info
                this.frameCount++;
                this.currentFrame = frameData;
                this.resolution = { width: img.width, height: img.height };
                
                // Update performance stats
                const renderTime = performance.now() - startTime;
                this.updateRenderStats(renderTime);
                
                // Update FPS
                this.updateFPS();
                
                // Update UI
                this.updateVideoInfo();
                
                // Call callback
                if (this.onFrameRendered) {
                    this.onFrameRendered({
                        frameCount: this.frameCount,
                        fps: this.fps,
                        resolution: this.resolution,
                        renderTime: renderTime,
                        metadata: metadata
                    });
                }
            };
            
            img.onerror = () => {
                console.error('Failed to load frame image');
                this.renderStats.droppedFrames++;
            };
            
            // Set image source (base64 data)
            if (frameData.startsWith('data:')) {
                img.src = frameData;
            } else {
                img.src = `data:image/jpeg;base64,${frameData}`;
            }
            
            return true;
            
        } catch (error) {
            console.error('Error rendering frame:', error);
            this.renderStats.droppedFrames++;
            return false;
        }
    }
    
    calculateDrawDimensions(imgWidth, imgHeight) {
        const canvasWidth = this.canvas.width;
        const canvasHeight = this.canvas.height;
        
        // Calculate aspect ratios
        const imgAspect = imgWidth / imgHeight;
        const canvasAspect = canvasWidth / canvasHeight;
        
        let drawWidth, drawHeight, x, y;
        
        if (imgAspect > canvasAspect) {
            // Image is wider than canvas
            drawWidth = canvasWidth;
            drawHeight = canvasWidth / imgAspect;
            x = 0;
            y = (canvasHeight - drawHeight) / 2;
        } else {
            // Image is taller than canvas
            drawWidth = canvasHeight * imgAspect;
            drawHeight = canvasHeight;
            x = (canvasWidth - drawWidth) / 2;
            y = 0;
        }
        
        return { x, y, width: drawWidth, height: drawHeight };
    }
    
    updateRenderStats(renderTime) {
        this.renderStats.framesRendered++;
        this.renderStats.totalRenderTime += renderTime;
        this.renderStats.avgRenderTime = this.renderStats.totalRenderTime / this.renderStats.framesRendered;
        
        if (this.onStatsUpdate) {
            this.onStatsUpdate(this.renderStats);
        }
    }
    
    updateFPS() {
        const currentTime = performance.now();
        
        if (this.lastFrameTime > 0) {
            const frameInterval = currentTime - this.lastFrameTime;
            
            // Add to buffer
            this.frameBuffer.push(frameInterval);
            if (this.frameBuffer.length > this.maxBufferSize) {
                this.frameBuffer.shift();
            }
            
            // Calculate average FPS
            if (this.frameBuffer.length > 1) {
                const avgInterval = this.frameBuffer.reduce((a, b) => a + b) / this.frameBuffer.length;
                this.fps = Math.round(1000 / avgInterval);
            }
        }
        
        this.lastFrameTime = currentTime;
    }
    
    updateVideoInfo() {
        // Update FPS counter
        const fpsElement = document.getElementById('fpsCounter');
        if (fpsElement) {
            fpsElement.textContent = `FPS: ${this.fps}`;
        }
        
        // Update resolution info
        const resolutionElement = document.getElementById('resolutionInfo');
        if (resolutionElement) {
            resolutionElement.textContent = `Çözünürlük: ${this.resolution.width}x${this.resolution.height}`;
        }
    }
    
    startRendering() {
        if (this.isRendering) {
            return;
        }
        
        this.isRendering = true;
        this.showOverlay(true);
        this.updateOverlayMessage('Video stream başlatılıyor...');
        
        console.log('Video rendering started');
    }
    
    stopRendering() {
        if (!this.isRendering) {
            return;
        }
        
        this.isRendering = false;
        this.clearCanvas();
        this.showOverlay(true);
        this.updateOverlayMessage('Video stream durduruldu', false);
        
        // Reset stats
        this.frameCount = 0;
        this.fps = 0;
        this.frameBuffer = [];
        this.lastFrameTime = 0;
        
        this.updateVideoInfo();
        
        console.log('Video rendering stopped');
    }
    
    getStats() {
        return {
            ...this.renderStats,
            frameCount: this.frameCount,
            fps: this.fps,
            resolution: this.resolution,
            isRendering: this.isRendering,
            bufferSize: this.frameBuffer.length
        };
    }
    
    resetStats() {
        this.renderStats = {
            framesRendered: 0,
            totalRenderTime: 0,
            avgRenderTime: 0,
            droppedFrames: 0
        };
        this.frameCount = 0;
        this.frameBuffer = [];
    }
    
    // Canvas interaction methods
    getCanvasCoordinates(event) {
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;
        
        return {
            x: (event.clientX - rect.left) * scaleX,
            y: (event.clientY - rect.top) * scaleY
        };
    }
    
    addCanvasClickHandler(handler) {
        this.canvas.addEventListener('click', (event) => {
            const coords = this.getCanvasCoordinates(event);
            handler(coords, event);
        });
    }
    
    // Screenshot functionality
    takeScreenshot() {
        if (!this.currentFrame) {
            console.warn('No frame available for screenshot');
            return null;
        }
        
        try {
            const dataURL = this.canvas.toDataURL('image/png');
            return dataURL;
        } catch (error) {
            console.error('Error taking screenshot:', error);
            return null;
        }
    }
    
    downloadScreenshot(filename = null) {
        const screenshot = this.takeScreenshot();
        if (!screenshot) {
            return false;
        }
        
        const link = document.createElement('a');
        link.download = filename || `screenshot_${Date.now()}.png`;
        link.href = screenshot;
        link.click();
        
        return true;
    }
}

// Global video renderer instance
window.videoRenderer = new VideoRenderer('videoCanvas', 'videoOverlay');
