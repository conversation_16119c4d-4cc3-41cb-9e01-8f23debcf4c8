/**
 * AI Visualizer - AI analiz sonuçlarını görselleştirir
 */

class AIVisualizer {
    constructor(canvasId, detectionListId) {
        this.canvas = document.getElementById(canvasId);
        this.detectionsList = document.getElementById(detectionListId);
        this.ctx = this.canvas.getContext('2d');
        
        // Current detections
        this.currentDetections = [];
        this.lastAnalysisTime = 0;
        this.totalDetections = 0;
        
        // Visualization settings
        this.settings = {
            showBoundingBoxes: true,
            showLabels: true,
            showConfidence: true,
            showCenterPoints: true,
            boxLineWidth: 2,
            fontSize: 14,
            fontFamily: 'Arial, sans-serif',
            colors: [
                '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
                '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
            ]
        };
        
        // Animation
        this.animationId = null;
        this.isAnimating = false;
        
        // Statistics
        this.stats = {
            totalAnalyses: 0,
            totalDetections: 0,
            avgDetectionsPerFrame: 0,
            avgProcessingTime: 0,
            detectionHistory: []
        };
        
        // Event handlers
        this.onDetectionUpdate = null;
        this.onStatsUpdate = null;
    }
    
    updateDetections(analysisResult) {
        if (!analysisResult) {
            return;
        }
        
        try {
            this.currentDetections = analysisResult.detections || [];
            this.lastAnalysisTime = analysisResult.processing_time || 0;
            
            // Update statistics
            this.updateStats(analysisResult);
            
            // Update UI
            this.updateDetectionsList();
            this.updateAnalysisInfo();
            
            // Draw detections on canvas
            this.drawDetections();
            
            // Call callback
            if (this.onDetectionUpdate) {
                this.onDetectionUpdate(this.currentDetections);
            }
            
        } catch (error) {
            console.error('Error updating detections:', error);
        }
    }
    
    drawDetections() {
        if (!this.settings.showBoundingBoxes || this.currentDetections.length === 0) {
            return;
        }
        
        // Clear previous drawings
        this.clearOverlay();
        
        // Draw each detection
        this.currentDetections.forEach((detection, index) => {
            this.drawDetection(detection, index);
        });
    }
    
    drawDetection(detection, index) {
        const color = this.settings.colors[index % this.settings.colors.length];
        const bbox = detection.bbox;
        
        if (!bbox || bbox.length !== 4) {
            return;
        }
        
        const [x1, y1, x2, y2] = bbox;
        const width = x2 - x1;
        const height = y2 - y1;
        
        // Set drawing style
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = this.settings.boxLineWidth;
        this.ctx.fillStyle = color;
        
        // Draw bounding box
        if (this.settings.showBoundingBoxes) {
            this.ctx.strokeRect(x1, y1, width, height);
        }
        
        // Draw center point
        if (this.settings.showCenterPoints && detection.center) {
            const [cx, cy] = detection.center;
            this.ctx.beginPath();
            this.ctx.arc(cx, cy, 3, 0, 2 * Math.PI);
            this.ctx.fill();
        }
        
        // Draw label
        if (this.settings.showLabels) {
            this.drawLabel(detection, x1, y1, color);
        }
    }
    
    drawLabel(detection, x, y, color) {
        const label = detection.class_name || 'Unknown';
        const confidence = detection.confidence || 0;
        
        // Prepare label text
        let labelText = label;
        if (this.settings.showConfidence) {
            labelText += ` ${(confidence * 100).toFixed(1)}%`;
        }
        
        // Set font
        this.ctx.font = `${this.settings.fontSize}px ${this.settings.fontFamily}`;
        this.ctx.textAlign = 'left';
        this.ctx.textBaseline = 'top';
        
        // Measure text
        const textMetrics = this.ctx.measureText(labelText);
        const textWidth = textMetrics.width;
        const textHeight = this.settings.fontSize;
        
        // Background padding
        const padding = 4;
        const bgWidth = textWidth + padding * 2;
        const bgHeight = textHeight + padding * 2;
        
        // Adjust position if label goes outside canvas
        let labelX = x;
        let labelY = y - bgHeight;
        
        if (labelY < 0) {
            labelY = y;
        }
        if (labelX + bgWidth > this.canvas.width) {
            labelX = this.canvas.width - bgWidth;
        }
        
        // Draw background
        this.ctx.fillStyle = color;
        this.ctx.fillRect(labelX, labelY, bgWidth, bgHeight);
        
        // Draw text
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.fillText(labelText, labelX + padding, labelY + padding);
    }
    
    clearOverlay() {
        // Save current canvas state
        const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
        
        // This method should be called after the video frame is drawn
        // We don't actually clear here, just prepare for drawing overlays
    }
    
    updateDetectionsList() {
        if (!this.detectionsList) {
            return;
        }
        
        if (this.currentDetections.length === 0) {
            this.detectionsList.innerHTML = '<p class="no-detections">Henüz tespit edilmiş nesne yok</p>';
            return;
        }
        
        const detectionsHTML = this.currentDetections.map((detection, index) => {
            const color = this.settings.colors[index % this.settings.colors.length];
            const confidence = (detection.confidence * 100).toFixed(1);
            const position = detection.center ? 
                `(${detection.center[0]}, ${detection.center[1]})` : 
                'Bilinmiyor';
            
            return `
                <div class="detection-item" style="border-left-color: ${color}">
                    <div class="detection-header">
                        <span class="detection-name">${detection.class_name}</span>
                        <span class="detection-confidence">${confidence}%</span>
                    </div>
                    <div class="detection-position">Pozisyon: ${position}</div>
                </div>
            `;
        }).join('');
        
        this.detectionsList.innerHTML = detectionsHTML;
    }
    
    updateAnalysisInfo() {
        // Update detection count
        const detectionCountElement = document.getElementById('detectionCount');
        if (detectionCountElement) {
            detectionCountElement.textContent = `Tespit: ${this.currentDetections.length}`;
        }
        
        // Update processing time
        const processingTimeElement = document.getElementById('processingTime');
        if (processingTimeElement) {
            const timeMs = (this.lastAnalysisTime * 1000).toFixed(1);
            processingTimeElement.textContent = `İşleme: ${timeMs}ms`;
        }
    }
    
    updateStats(analysisResult) {
        this.stats.totalAnalyses++;
        this.stats.totalDetections += this.currentDetections.length;
        
        // Calculate average detections per frame
        this.stats.avgDetectionsPerFrame = this.stats.totalDetections / this.stats.totalAnalyses;
        
        // Update processing time average
        const alpha = 0.1; // Exponential moving average factor
        if (this.stats.avgProcessingTime === 0) {
            this.stats.avgProcessingTime = this.lastAnalysisTime;
        } else {
            this.stats.avgProcessingTime = alpha * this.lastAnalysisTime + 
                                         (1 - alpha) * this.stats.avgProcessingTime;
        }
        
        // Add to history (keep last 100 entries)
        this.stats.detectionHistory.push({
            timestamp: Date.now(),
            detectionCount: this.currentDetections.length,
            processingTime: this.lastAnalysisTime
        });
        
        if (this.stats.detectionHistory.length > 100) {
            this.stats.detectionHistory.shift();
        }
        
        // Call callback
        if (this.onStatsUpdate) {
            this.onStatsUpdate(this.stats);
        }
    }
    
    getDetectionByPosition(x, y) {
        return this.currentDetections.find(detection => {
            if (!detection.bbox) return false;
            
            const [x1, y1, x2, y2] = detection.bbox;
            return x >= x1 && x <= x2 && y >= y1 && y <= y2;
        });
    }
    
    highlightDetection(detection) {
        if (!detection || !detection.bbox) {
            return;
        }
        
        const [x1, y1, x2, y2] = detection.bbox;
        const width = x2 - x1;
        const height = y2 - y1;
        
        // Draw highlight
        this.ctx.strokeStyle = '#FFFF00';
        this.ctx.lineWidth = 4;
        this.ctx.strokeRect(x1 - 2, y1 - 2, width + 4, height + 4);
        
        // Auto-remove highlight after 2 seconds
        setTimeout(() => {
            this.drawDetections();
        }, 2000);
    }
    
    exportDetections() {
        return {
            timestamp: Date.now(),
            detections: this.currentDetections,
            stats: this.stats,
            settings: this.settings
        };
    }
    
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.drawDetections(); // Redraw with new settings
    }
    
    getStats() {
        return {
            ...this.stats,
            currentDetections: this.currentDetections.length,
            lastAnalysisTime: this.lastAnalysisTime,
            settings: this.settings
        };
    }
    
    resetStats() {
        this.stats = {
            totalAnalyses: 0,
            totalDetections: 0,
            avgDetectionsPerFrame: 0,
            avgProcessingTime: 0,
            detectionHistory: []
        };
        
        this.currentDetections = [];
        this.updateDetectionsList();
        this.updateAnalysisInfo();
    }
    
    // Class-specific color mapping
    getClassColor(className) {
        const classColors = {
            'person': '#FF6B6B',
            'car': '#4ECDC4',
            'bicycle': '#45B7D1',
            'motorcycle': '#96CEB4',
            'bus': '#FFEAA7',
            'truck': '#DDA0DD',
            'cat': '#98D8C8',
            'dog': '#F7DC6F',
            'bird': '#BB8FCE',
            'bottle': '#85C1E9'
        };
        
        return classColors[className] || this.settings.colors[0];
    }
    
    // Filter detections by confidence threshold
    filterByConfidence(threshold) {
        this.currentDetections = this.currentDetections.filter(
            detection => detection.confidence >= threshold
        );
        this.updateDetectionsList();
        this.drawDetections();
    }
    
    // Filter detections by class
    filterByClass(classNames) {
        if (!Array.isArray(classNames)) {
            classNames = [classNames];
        }
        
        this.currentDetections = this.currentDetections.filter(
            detection => classNames.includes(detection.class_name)
        );
        this.updateDetectionsList();
        this.drawDetections();
    }
}

// Global AI visualizer instance
window.aiVisualizer = new AIVisualizer('videoCanvas', 'detectionsList');
