"""
Image Processor - G<PERSON><PERSON><PERSON><PERSON><PERSON> işleme ve optimizasyon modülü
"""
import asyncio
import time
from typing import Optional, Dict, Any, Tuple, List
import numpy as np
import cv2
from dataclasses import dataclass, asdict


@dataclass
class ProcessingStats:
    """İşleme istatistikleri"""
    frames_processed: int = 0
    avg_processing_time: float = 0.0
    last_processing_time: float = 0.0
    total_processing_time: float = 0.0


class ImageProcessor:
    """
    <PERSON>örüntü işleme ve optimizasyon sınıfı
    """
    
    def __init__(self, enable_preprocessing: bool = True,
                 enable_postprocessing: bool = True):
        """
        Args:
            enable_preprocessing: Ön işleme etkin mi
            enable_postprocessing: Son işleme etkin mi
        """
        self.enable_preprocessing = enable_preprocessing
        self.enable_postprocessing = enable_postprocessing
        self.stats = ProcessingStats()
        self._processing_times = []
        
        # <PERSON>şleme filtreleri
        self.filters = {
            "blur": False,
            "sharpen": False,
            "denoise": False,
            "enhance": False,
            "normalize": False
        }
        
        # Kernel'lar
        self.sharpen_kernel = np.array([[-1,-1,-1],
                                       [-1, 9,-1],
                                       [-1,-1,-1]])
    
    async def process_frame(self, frame: np.ndarray,
                           apply_filters: Optional[List[str]] = None) -> Optional[np.ndarray]:
        """
        Frame'i işle
        
        Args:
            frame: Giriş frame'i
            apply_filters: Uygulanacak filtreler listesi
            
        Returns:
            np.ndarray: İşlenmiş frame veya None
        """
        if frame is None:
            return None
        
        start_time = time.time()
        
        try:
            processed_frame = frame.copy()
            
            # Ön işleme
            if self.enable_preprocessing:
                processed_frame = await self._preprocess(processed_frame)
            
            # Filtreler
            if apply_filters:
                for filter_name in apply_filters:
                    processed_frame = await self._apply_filter(processed_frame, filter_name)
            
            # Son işleme
            if self.enable_postprocessing:
                processed_frame = await self._postprocess(processed_frame)
            
            # İstatistikleri güncelle
            processing_time = time.time() - start_time
            self._update_stats(processing_time)
            
            return processed_frame
            
        except Exception as e:
            print(f"Image processing error: {e}")
            return frame  # Hata durumunda orijinal frame'i döndür
    
    async def _preprocess(self, frame: np.ndarray) -> np.ndarray:
        """Ön işleme adımları"""
        # Gürültü azaltma (hafif)
        if self.filters.get("denoise", False):
            frame = cv2.fastNlMeansDenoisingColored(frame, None, 10, 10, 7, 21)
        
        # Normalizasyon
        if self.filters.get("normalize", False):
            frame = cv2.normalize(frame, None, 0, 255, cv2.NORM_MINMAX)
        
        return frame
    
    async def _postprocess(self, frame: np.ndarray) -> np.ndarray:
        """Son işleme adımları"""
        # Renk iyileştirme
        if self.filters.get("enhance", False):
            # LAB color space'e çevir
            lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            
            # L kanalına CLAHE uygula
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            l = clahe.apply(l)
            
            # Geri birleştir
            lab = cv2.merge([l, a, b])
            frame = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
        
        return frame
    
    async def _apply_filter(self, frame: np.ndarray, filter_name: str) -> np.ndarray:
        """Belirli bir filtre uygula"""
        if filter_name == "blur":
            return cv2.GaussianBlur(frame, (5, 5), 0)
        
        elif filter_name == "sharpen":
            return cv2.filter2D(frame, -1, self.sharpen_kernel)
        
        elif filter_name == "denoise":
            return cv2.fastNlMeansDenoisingColored(frame, None, 10, 10, 7, 21)
        
        elif filter_name == "enhance":
            # Histogram eşitleme
            yuv = cv2.cvtColor(frame, cv2.COLOR_BGR2YUV)
            yuv[:,:,0] = cv2.equalizeHist(yuv[:,:,0])
            return cv2.cvtColor(yuv, cv2.COLOR_YUV2BGR)
        
        elif filter_name == "normalize":
            return cv2.normalize(frame, None, 0, 255, cv2.NORM_MINMAX)
        
        else:
            return frame
    
    async def resize_frame(self, frame: np.ndarray, width: int, height: int,
                          interpolation: int = cv2.INTER_LINEAR) -> np.ndarray:
        """
        Frame'i yeniden boyutlandır
        
        Args:
            frame: Giriş frame'i
            width: Hedef genişlik
            height: Hedef yükseklik
            interpolation: İnterpolasyon metodu
            
        Returns:
            np.ndarray: Yeniden boyutlandırılmış frame
        """
        return cv2.resize(frame, (width, height), interpolation=interpolation)
    
    async def crop_frame(self, frame: np.ndarray, x: int, y: int,
                        width: int, height: int) -> np.ndarray:
        """
        Frame'i kırp
        
        Args:
            frame: Giriş frame'i
            x: Başlangıç x koordinatı
            y: Başlangıç y koordinatı
            width: Kırpma genişliği
            height: Kırpma yüksekliği
            
        Returns:
            np.ndarray: Kırpılmış frame
        """
        h, w = frame.shape[:2]
        
        # Sınırları kontrol et
        x = max(0, min(x, w))
        y = max(0, min(y, h))
        width = min(width, w - x)
        height = min(height, h - y)
        
        return frame[y:y+height, x:x+width]
    
    async def rotate_frame(self, frame: np.ndarray, angle: float) -> np.ndarray:
        """
        Frame'i döndür
        
        Args:
            frame: Giriş frame'i
            angle: Dönüş açısı (derece)
            
        Returns:
            np.ndarray: Döndürülmüş frame
        """
        h, w = frame.shape[:2]
        center = (w // 2, h // 2)
        
        # Dönüş matrisi
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        
        # Döndür
        rotated = cv2.warpAffine(frame, rotation_matrix, (w, h))
        
        return rotated
    
    async def add_overlay(self, frame: np.ndarray, overlay: np.ndarray,
                         x: int, y: int, alpha: float = 0.7) -> np.ndarray:
        """
        Frame'e overlay ekle
        
        Args:
            frame: Ana frame
            overlay: Overlay görüntüsü
            x: Overlay x pozisyonu
            y: Overlay y pozisyonu
            alpha: Şeffaflık (0.0-1.0)
            
        Returns:
            np.ndarray: Overlay eklenmiş frame
        """
        h, w = frame.shape[:2]
        oh, ow = overlay.shape[:2]
        
        # Sınırları kontrol et
        if x + ow > w or y + oh > h or x < 0 or y < 0:
            return frame
        
        # ROI (Region of Interest) al
        roi = frame[y:y+oh, x:x+ow]
        
        # Blend
        blended = cv2.addWeighted(roi, 1-alpha, overlay, alpha, 0)
        
        # Geri yerleştir
        result = frame.copy()
        result[y:y+oh, x:x+ow] = blended
        
        return result
    
    def set_filter(self, filter_name: str, enabled: bool) -> None:
        """Filtre durumunu ayarla"""
        if filter_name in self.filters:
            self.filters[filter_name] = enabled
    
    def get_filters(self) -> Dict[str, bool]:
        """Aktif filtreleri döndür"""
        return self.filters.copy()
    
    def _update_stats(self, processing_time: float) -> None:
        """İstatistikleri güncelle"""
        self.stats.frames_processed += 1
        self.stats.total_processing_time += processing_time
        self.stats.last_processing_time = processing_time
        
        # Processing time tracking
        self._processing_times.append(processing_time)
        if len(self._processing_times) > 100:
            self._processing_times.pop(0)
        
        self.stats.avg_processing_time = sum(self._processing_times) / len(self._processing_times)
    
    def get_stats(self) -> Dict[str, Any]:
        """İstatistikleri döndür"""
        stats_dict = asdict(self.stats)
        stats_dict.update({
            "avg_processing_time_ms": round(self.stats.avg_processing_time * 1000, 2),
            "total_processing_time_s": round(self.stats.total_processing_time, 2),
            "active_filters": [name for name, enabled in self.filters.items() if enabled],
            "preprocessing_enabled": self.enable_preprocessing,
            "postprocessing_enabled": self.enable_postprocessing
        })
        return stats_dict
    
    def reset_stats(self) -> None:
        """İstatistikleri sıfırla"""
        self.stats = ProcessingStats()
        self._processing_times = []
    
    async def benchmark_processing(self, frame: np.ndarray, 
                                  filters: List[str], iterations: int = 10) -> Dict[str, float]:
        """
        İşleme performansını test et
        
        Args:
            frame: Test frame'i
            filters: Test edilecek filtreler
            iterations: Test sayısı
            
        Returns:
            Dict: Benchmark sonuçları
        """
        times = []
        
        for _ in range(iterations):
            start_time = time.time()
            await self.process_frame(frame, filters)
            end_time = time.time()
            times.append(end_time - start_time)
        
        return {
            "avg_time_ms": round(sum(times) / len(times) * 1000, 2),
            "min_time_ms": round(min(times) * 1000, 2),
            "max_time_ms": round(max(times) * 1000, 2),
            "filters_tested": filters,
            "iterations": iterations
        }
